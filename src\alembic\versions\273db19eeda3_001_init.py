"""001_init

Revision ID: 273db19eeda3
Revises: 
Create Date: 2025-08-05 23:01:09.770261

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '273db19eeda3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assessments',
    sa.Column('assessment_id', sa.UUID(), nullable=False),
    sa.Column('code', sa.Enum('MOCK', 'REal', 'QUIZ', name='assessmenttype'), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('version_int', sa.Integer(), nullable=False),
    sa.Column('active', sa.<PERSON>(), nullable=False),
    sa.Column('directions', sa.Text(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('notes', sa.JSON(), nullable=True),
    sa.Column('reference', sa.JSON(), nullable=True),
    sa.Column('conclusion', sa.Text(), nullable=True),
    sa.Column('timing', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('assessment_id')
    )
    op.create_table('countries',
    sa.Column('country_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('flag', sa.String(), nullable=False),
    sa.Column('calling_code', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('country_id')
    )
    countries_table = sa.table('countries',
        sa.Column('country_id', sa.Integer),
        sa.Column('name', sa.String),
        sa.Column('code', sa.String),
        sa.Column('flag', sa.String),
        sa.Column('calling_code', sa.String)
    )

    op.bulk_insert(
   countries_table,
    [
  {
    "country_id": 1,
    "name": "Afghanistan",
    "code": "AF",
    "flag": "https://www.worldometers.info/img/flags/small/tn_af-flag.gif",
    "calling_code": "+93"
  },
  {
    "country_id": 2,
    "name": "Albania",
    "code": "AL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_al-flag.gif",
    "calling_code": "+355"
  },
  {
    "country_id": 3,
    "name": "Algeria",
    "code": "DZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ag-flag.gif",
    "calling_code": "+213"
  },
  {
    "country_id": 4,
    "name": "Andorra",
    "code": "AD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_an-flag.gif",
    "calling_code": "+376"
  },
  {
    "country_id": 5,
    "name": "Angola",
    "code": "AO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ao-flag.gif",
    "calling_code": "+244"
  },
  {
    "country_id": 6,
    "name": "Antigua and Barbuda",
    "code": "AG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ac-flag.gif",
    "calling_code": "+1 (268)"
  },
  {
    "country_id": 7,
    "name": "Argentina",
    "code": "AR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ar-flag.gif",
    "calling_code": "+54"
  },
  {
    "country_id": 8,
    "name": "Armenia",
    "code": "AM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_am-flag.gif",
    "calling_code": "+374"
  },
  {
    "country_id": 9,
    "name": "Australia",
    "code": "AU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_as-flag.gif",
    "calling_code": "+61"
  },
  {
    "country_id": 10,
    "name": "Austria",
    "code": "AT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_au-flag.gif",
    "calling_code": "+43"
  },
  {
    "country_id": 11,
    "name": "Azerbaijan",
    "code": "AZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_aj-flag.gif",
    "calling_code": "+994"
  },
  {
    "country_id": 12,
    "name": "Bahamas (the)",
    "code": "BS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bf-flag.gif",
    "calling_code": "+1 (242)"
  },
  {
    "country_id": 13,
    "name": "Bahrain",
    "code": "BH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ba-flag.gif",
    "calling_code": "+973"
  },
  {
    "country_id": 14,
    "name": "Bangladesh",
    "code": "BD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bg-flag.gif",
    "calling_code": "+880"
  },
  {
    "country_id": 15,
    "name": "Barbados",
    "code": "BB",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bb-flag.gif",
    "calling_code": "+1 (246)"
  },
  {
    "country_id": 16,
    "name": "Belarus",
    "code": "BY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bo-flag.gif",
    "calling_code": "+375"
  },
  {
    "country_id": 17,
    "name": "Belgium",
    "code": "BE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_be-flag.gif",
    "calling_code": "+32"
  },
  {
    "country_id": 18,
    "name": "Belize",
    "code": "BZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bh-flag.gif",
    "calling_code": "+501"
  },
  {
    "country_id": 19,
    "name": "Benin",
    "code": "BJ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bn-flag.gif",
    "calling_code": "+229"
  },
  {
    "country_id": 20,
    "name": "Bhutan",
    "code": "BT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bt-flag.gif",
    "calling_code": "+975"
  },
  {
    "country_id": 21,
    "name": "Bolivia (Plurinational State of)",
    "code": "BO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bl-flag.gif",
    "calling_code": "+591"
  },
  {
    "country_id": 22,
    "name": "Bosnia and Herzegovina",
    "code": "BA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bk-flag.gif",
    "calling_code": "+387"
  },
  {
    "country_id": 23,
    "name": "Botswana",
    "code": "BW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bc-flag.gif",
    "calling_code": "+267"
  },
  {
    "country_id": 24,
    "name": "Brazil",
    "code": "BR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_br-flag.gif",
    "calling_code": "+55"
  },
  {
    "country_id": 25,
    "name": "Brunei Darussalam",
    "code": "BN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bx-flag.gif",
    "calling_code": "+673"
  },
  {
    "country_id": 26,
    "name": "Bulgaria",
    "code": "BG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bu-flag.gif",
    "calling_code": "+359"
  },
  {
    "country_id": 27,
    "name": "Burkina Faso",
    "code": "BF",
    "flag": "https://www.worldometers.info/img/flags/small/tn_uv-flag.gif",
    "calling_code": "+226"
  },
  {
    "country_id": 28,
    "name": "Burundi",
    "code": "BI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_by-flag.gif",
    "calling_code": "+257"
  },
  {
    "country_id": 29,
    "name": "Cabo Verde",
    "code": "CV",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cv-flag.gif",
    "calling_code": "+238"
  },
  {
    "country_id": 30,
    "name": "Cambodia",
    "code": "KH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cb-flag.gif",
    "calling_code": "+855"
  },
  {
    "country_id": 31,
    "name": "Cameroon",
    "code": "CM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cm-flag.gif",
    "calling_code": "+237"
  },
  {
    "country_id": 32,
    "name": "Canada",
    "code": "CA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ca-flag.gif",
    "calling_code": "+1"
  },
  {
    "country_id": 33,
    "name": "Central African Republic (the)",
    "code": "CF",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ct-flag.gif",
    "calling_code": "+236"
  },
  {
    "country_id": 34,
    "name": "Chad",
    "code": "TD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cd-flag.gif",
    "calling_code": "+235"
  },
  {
    "country_id": 35,
    "name": "Chile",
    "code": "CL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ci-flag.gif",
    "calling_code": "+56"
  },
  {
    "country_id": 36,
    "name": "China",
    "code": "CN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ch-flag.gif",
    "calling_code": "+86"
  },
  {
    "country_id": 37,
    "name": "Colombia",
    "code": "CO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_co-flag.gif",
    "calling_code": "+57"
  },
  {
    "country_id": 38,
    "name": "Comoros (the)",
    "code": "KM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cn-flag.gif",
    "calling_code": "+269"
  },
  {
    "country_id": 39,
    "name": "Congo (the Democratic Republic of the)",
    "code": "CD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_congo-flag.gif",
    "calling_code": "+243"
  },
  {
    "country_id": 40,
    "name": "Congo (the)",
    "code": "CG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cg-flag.gif",
    "calling_code": "+242"
  },
  {
    "country_id": 41,
    "name": "Costa Rica",
    "code": "CR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cs-flag.gif",
    "calling_code": "+506"
  },
  {
    "country_id": 42,
    "name": "Côte d'Ivoire",
    "code": "CI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_iv-flag.gif",
    "calling_code": "+225"
  },
  {
    "country_id": 43,
    "name": "Croatia",
    "code": "HR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_hr-flag.gif",
    "calling_code": "+385"
  },
  {
    "country_id": 44,
    "name": "Cuba",
    "code": "CU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cu-flag.gif",
    "calling_code": "+53"
  },
  {
    "country_id": 45,
    "name": "Cyprus",
    "code": "CY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_cy-flag.gif",
    "calling_code": "+357"
  },
  {
    "country_id": 46,
    "name": "Czechia",
    "code": "CZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ez-flag.gif",
    "calling_code": "+420"
  },
  {
    "country_id": 47,
    "name": "Denmark",
    "code": "DK",
    "flag": "https://www.worldometers.info/img/flags/small/tn_da-flag.gif",
    "calling_code": "+45"
  },
  {
    "country_id": 48,
    "name": "Djibouti",
    "code": "DJ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_dj-flag.gif",
    "calling_code": "+253"
  },
  {
    "country_id": 49,
    "name": "Dominica",
    "code": "DM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_do-flag.gif",
    "calling_code": "+1 (767)"
  },
  {
    "country_id": 50,
    "name": "Dominican Republic (the)",
    "code": "DO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_dr-flag.gif",
    "calling_code": "+1 (809, 829, 849)"
  },
  {
    "country_id": 51,
    "name": "Ecuador",
    "code": "EC",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ec-flag.gif",
    "calling_code": "+593"
  },
  {
    "country_id": 52,
    "name": "Egypt",
    "code": "EG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_eg-flag.gif",
    "calling_code": "+20"
  },
  {
    "country_id": 53,
    "name": "El Salvador",
    "code": "SV",
    "flag": "https://www.worldometers.info/img/flags/small/tn_es-flag.gif",
    "calling_code": "+503"
  },
  {
    "country_id": 54,
    "name": "Equatorial Guinea",
    "code": "GQ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ek-flag.gif",
    "calling_code": "+240"
  },
  {
    "country_id": 55,
    "name": "Eritrea",
    "code": "ER",
    "flag": "https://www.worldometers.info/img/flags/small/tn_er-flag.gif",
    "calling_code": "+291"
  },
  {
    "country_id": 56,
    "name": "Estonia",
    "code": "EE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_en-flag.gif",
    "calling_code": "+372"
  },
  {
    "country_id": 57,
    "name": "Eswatini",
    "code": "SZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_wz-flag.gif",
    "calling_code": "+268"
  },
  {
    "country_id": 58,
    "name": "Ethiopia",
    "code": "ET",
    "flag": "https://www.worldometers.info/img/flags/small/tn_et-flag.gif",
    "calling_code": "+251"
  },
  {
    "country_id": 59,
    "name": "Fiji",
    "code": "FJ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_fj-flag.gif",
    "calling_code": "+679"
  },
  {
    "country_id": 60,
    "name": "Finland",
    "code": "FI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_fi-flag.gif",
    "calling_code": "+358"
  },
  {
    "country_id": 61,
    "name": "France",
    "code": "FR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_fr-flag.gif",
    "calling_code": "+33"
  },
  {
    "country_id": 62,
    "name": "Gabon",
    "code": "GA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gb-flag.gif",
    "calling_code": "+241"
  },
  {
    "country_id": 63,
    "name": "Gambia (the)",
    "code": "GM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ga-flag.gif",
    "calling_code": "+220"
  },
  {
    "country_id": 64,
    "name": "Georgia",
    "code": "GE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gg-flag.gif",
    "calling_code": "+995"
  },
  {
    "country_id": 65,
    "name": "Germany",
    "code": "DE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gm-flag.gif",
    "calling_code": "+49"
  },
  {
    "country_id": 66,
    "name": "Ghana",
    "code": "GH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gh-flag.gif",
    "calling_code": "+233"
  },
  {
    "country_id": 67,
    "name": "Greece",
    "code": "GR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gr-flag.gif",
    "calling_code": "+30"
  },
  {
    "country_id": 68,
    "name": "Grenada",
    "code": "GD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gj-flag.gif",
    "calling_code": "+1 (473)"
  },
  {
    "country_id": 69,
    "name": "Guatemala",
    "code": "GT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gt-flag.gif",
    "calling_code": "+502"
  },
  {
    "country_id": 70,
    "name": "Guinea",
    "code": "GN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gv-flag.gif",
    "calling_code": "+224"
  },
  {
    "country_id": 71,
    "name": "Guinea-Bissau",
    "code": "GW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pu-flag.gif",
    "calling_code": "+245"
  },
  {
    "country_id": 72,
    "name": "Guyana",
    "code": "GY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_gy-flag.gif",
    "calling_code": "+592"
  },
  {
    "country_id": 73,
    "name": "Haiti",
    "code": "HT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ha-flag.gif",
    "calling_code": "+509"
  },
  {
    "country_id": 74,
    "name": "Holy See (the)",
    "code": "VA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_vt-flag.gif",
    "calling_code": "+379"
  },
  {
    "country_id": 75,
    "name": "Honduras",
    "code": "HN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ho-flag.gif",
    "calling_code": "+504"
  },
  {
    "country_id": 76,
    "name": "Hungary",
    "code": "HU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_hu-flag.gif",
    "calling_code": "+36"
  },
  {
    "country_id": 77,
    "name": "Iceland",
    "code": "IS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ic-flag.gif",
    "calling_code": "+354"
  },
  {
    "country_id": 78,
    "name": "India",
    "code": "IN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_in-flag.gif",
    "calling_code": "+91"
  },
  {
    "country_id": 79,
    "name": "Indonesia",
    "code": "ID",
    "flag": "https://www.worldometers.info/img/flags/small/tn_id-flag.gif",
    "calling_code": "+62"
  },
  {
    "country_id": 80,
    "name": "Iran (Islamic Republic of)",
    "code": "IR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ir-flag.gif",
    "calling_code": "+98"
  },
  {
    "country_id": 81,
    "name": "Iraq",
    "code": "IQ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_iz-flag.gif",
    "calling_code": "+964"
  },
  {
    "country_id": 82,
    "name": "Ireland",
    "code": "IE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ei-flag.gif",
    "calling_code": "+353"
  },
  {
    "country_id": 83,
    "name": "Israel",
    "code": "IL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_is-flag.gif",
    "calling_code": "+972"
  },
  {
    "country_id": 84,
    "name": "Italy",
    "code": "IT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_it-flag.gif",
    "calling_code": "+39"
  },
  {
    "country_id": 85,
    "name": "Jamaica",
    "code": "JM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_jm-flag.gif",
    "calling_code": "+1 (876)"
  },
  {
    "country_id": 86,
    "name": "Japan",
    "code": "JP",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ja-flag.gif",
    "calling_code": "+81"
  },
  {
    "country_id": 87,
    "name": "Jordan",
    "code": "JO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_jo-flag.gif",
    "calling_code": "+962"
  },
  {
    "country_id": 88,
    "name": "Kazakhstan",
    "code": "KZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_kz-flag.gif",
    "calling_code": "+7"
  },
  {
    "country_id": 89,
    "name": "Kenya",
    "code": "KE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ke-flag.gif",
    "calling_code": "+254"
  },
  {
    "country_id": 90,
    "name": "Kiribati",
    "code": "KI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_kr-flag.gif",
    "calling_code": "+686"
  },
  {
    "country_id": 91,
    "name": "Korea (the Democratic People's Republic of)",
    "code": "KP",
    "flag": "https://www.worldometers.info/img/flags/small/tn_kn-flag.gif",
    "calling_code": "+850"
  },
  {
    "country_id": 92,
    "name": "Korea (the Republic of)",
    "code": "KR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ks-flag.gif",
    "calling_code": "+82"
  },
  {
    "country_id": 93,
    "name": "Kuwait",
    "code": "KW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ku-flag.gif",
    "calling_code": "+965"
  },
  {
    "country_id": 94,
    "name": "Kyrgyzstan",
    "code": "KG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_kg-flag.gif",
    "calling_code": "+996"
  },
  {
    "country_id": 95,
    "name": "Lao People's Democratic Republic (the)",
    "code": "LA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_la-flag.gif",
    "calling_code": "+856"
  },
  {
    "country_id": 96,
    "name": "Latvia",
    "code": "LV",
    "flag": "https://www.worldometers.info/img/flags/small/tn_lg-flag.gif",
    "calling_code": "+371"
  },
  {
    "country_id": 97,
    "name": "Lebanon",
    "code": "LB",
    "flag": "https://www.worldometers.info/img/flags/small/tn_le-flag.gif",
    "calling_code": "+961"
  },
  {
    "country_id": 98,
    "name": "Lesotho",
    "code": "LS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_lt-flag.gif",
    "calling_code": "+266"
  },
  {
    "country_id": 99,
    "name": "Liberia",
    "code": "LR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_li-flag.gif",
    "calling_code": "+231"
  },
  {
    "country_id": 100,
    "name": "Libya",
    "code": "LY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ly-flag.gif",
    "calling_code": "+218"
  },
  {
    "country_id": 101,
    "name": "Liechtenstein",
    "code": "LI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ls-flag.gif",
    "calling_code": "+423"
  },
  {
    "country_id": 102,
    "name": "Lithuania",
    "code": "LT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_lh-flag.gif",
    "calling_code": "+370"
  },
  {
    "country_id": 103,
    "name": "Luxembourg",
    "code": "LU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_lu-flag.gif",
    "calling_code": "+352"
  },
  {
    "country_id": 104,
    "name": "Madagascar",
    "code": "MG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ma-flag.gif",
    "calling_code": "+261"
  },
  {
    "country_id": 105,
    "name": "Malawi",
    "code": "MW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mi-flag.gif",
    "calling_code": "+265"
  },
  {
    "country_id": 106,
    "name": "Malaysia",
    "code": "MY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_my-flag.gif",
    "calling_code": "+60"
  },
  {
    "country_id": 107,
    "name": "Maldives",
    "code": "MV",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mv-flag.gif",
    "calling_code": "+960"
  },
  {
    "country_id": 108,
    "name": "Mali",
    "code": "ML",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ml-flag.gif",
    "calling_code": "+223"
  },
  {
    "country_id": 109,
    "name": "Malta",
    "code": "MT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mt-flag.gif",
    "calling_code": "+356"
  },
  {
    "country_id": 110,
    "name": "Marshall Islands (the)",
    "code": "MH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_rm-flag.gif",
    "calling_code": "+692"
  },
  {
    "country_id": 111,
    "name": "Mauritania",
    "code": "MR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mr-flag.gif",
    "calling_code": "+222"
  },
  {
    "country_id": 112,
    "name": "Mauritius",
    "code": "MU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mp-flag.gif",
    "calling_code": "+230"
  },
  {
    "country_id": 113,
    "name": "Mexico",
    "code": "MX",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mx-flag.gif",
    "calling_code": "+52"
  },
  {
    "country_id": 114,
    "name": "Micronesia (Federated States of)",
    "code": "FM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_fm-flag.gif",
    "calling_code": "+691"
  },
  {
    "country_id": 115,
    "name": "Moldova (the Republic of)",
    "code": "MD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_md-flag.gif",
    "calling_code": "+373"
  },
  {
    "country_id": 116,
    "name": "Monaco",
    "code": "MC",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mn-flag.gif",
    "calling_code": "+377"
  },
  {
    "country_id": 117,
    "name": "Mongolia",
    "code": "MN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mg-flag.gif",
    "calling_code": "+976"
  },
  {
    "country_id": 118,
    "name": "Montenegro",
    "code": "ME",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mj-flag.gif",
    "calling_code": "+382"
  },
  {
    "country_id": 119,
    "name": "Morocco",
    "code": "MA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mo-flag.gif",
    "calling_code": "+212"
  },
  {
    "country_id": 120,
    "name": "Mozambique",
    "code": "MZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mz-flag.gif",
    "calling_code": "+258"
  },
  {
    "country_id": 121,
    "name": "Myanmar",
    "code": "MM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bm-flag.gif",
    "calling_code": "+95"
  },
  {
    "country_id": 122,
    "name": "Namibia",
    "code": "NA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_wa-flag.gif",
    "calling_code": "+264"
  },
  {
    "country_id": 123,
    "name": "Nauru",
    "code": "NR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_nr-flag.gif",
    "calling_code": "+674"
  },
  {
    "country_id": 124,
    "name": "Nepal",
    "code": "NP",
    "flag": "https://www.worldometers.info/img/flags/small/tn_np-flag.gif",
    "calling_code": "+977"
  },
  {
    "country_id": 125,
    "name": "Netherlands (the)",
    "code": "NL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_nl-flag.gif",
    "calling_code": "+31"
  },
  {
    "country_id": 126,
    "name": "New Zealand",
    "code": "NZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_nz-flag.gif",
    "calling_code": "+64"
  },
  {
    "country_id": 127,
    "name": "Nicaragua",
    "code": "NI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_nu-flag.gif",
    "calling_code": "+505"
  },
  {
    "country_id": 128,
    "name": "Niger (the)",
    "code": "NE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ng-flag.gif",
    "calling_code": "+227"
  },
  {
    "country_id": 129,
    "name": "Nigeria",
    "code": "NG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ni-flag.gif",
    "calling_code": "+234"
  },
  {
    "country_id": 130,
    "name": "Norway",
    "code": "NO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_no-flag.gif",
    "calling_code": "+47"
  },
  {
    "country_id": 131,
    "name": "Oman",
    "code": "OM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mu-flag.gif",
    "calling_code": "+968"
  },
  {
    "country_id": 132,
    "name": "Pakistan",
    "code": "PK",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pk-flag.gif",
    "calling_code": "+92"
  },
  {
    "country_id": 133,
    "name": "Palau",
    "code": "PW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ps-flag.gif",
    "calling_code": "+680"
  },
  {
    "country_id": 134,
    "name": "Palestine, State of",
    "code": "PS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_palestine-flag.gif",
    "calling_code": "+970"
  },
  {
    "country_id": 135,
    "name": "Panama",
    "code": "PA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pm-flag.gif",
    "calling_code": "+507"
  },
  {
    "country_id": 136,
    "name": "Papua New Guinea",
    "code": "PG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pp-flag.gif",
    "calling_code": "+675"
  },
  {
    "country_id": 137,
    "name": "Paraguay",
    "code": "PY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pa-flag.gif",
    "calling_code": "+595"
  },
  {
    "country_id": 138,
    "name": "Peru",
    "code": "PE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pe-flag.gif",
    "calling_code": "+51"
  },
  {
    "country_id": 139,
    "name": "Philippines (the)",
    "code": "PH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_rp-flag.gif",
    "calling_code": "+63"
  },
  {
    "country_id": 140,
    "name": "Poland",
    "code": "PL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_pl-flag.gif",
    "calling_code": "+48"
  },
  {
    "country_id": 141,
    "name": "Portugal",
    "code": "PT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_po-flag.gif",
    "calling_code": "+351"
  },
  {
    "country_id": 142,
    "name": "Qatar",
    "code": "QA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_qa-flag.gif",
    "calling_code": "+974"
  },
  {
    "country_id": 143,
    "name": "Republic of North Macedonia",
    "code": "MK",
    "flag": "https://www.worldometers.info/img/flags/small/tn_mk-flag.gif",
    "calling_code": "+389"
  },
  {
    "country_id": 144,
    "name": "Romania",
    "code": "RO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ro-flag.gif",
    "calling_code": "+40"
  },
  {
    "country_id": 145,
    "name": "Russian Federation (the)",
    "code": "RU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_rs-flag.gif",
    "calling_code": "+7"
  },
  {
    "country_id": 146,
    "name": "Rwanda",
    "code": "RW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_rw-flag.gif",
    "calling_code": "+250"
  },
  {
    "country_id": 147,
    "name": "Saint Kitts and Nevis",
    "code": "KN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sc-flag.gif",
    "calling_code": "+1 (869)"
  },
  {
    "country_id": 148,
    "name": "Saint Lucia",
    "code": "LC",
    "flag": "https://www.worldometers.info/img/flags/small/tn_st-flag.gif",
    "calling_code": "+1 (758)"
  },
  {
    "country_id": 149,
    "name": "Saint Vincent and the Grenadines",
    "code": "VC",
    "flag": "https://www.worldometers.info/img/flags/small/tn_vc-flag.gif",
    "calling_code": "+1 (784)"
  },
  {
    "country_id": 150,
    "name": "Samoa",
    "code": "WS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ws-flag.gif",
    "calling_code": "+685"
  },
  {
    "country_id": 151,
    "name": "San Marino",
    "code": "SM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sm-flag.gif",
    "calling_code": "+378"
  },
  {
    "country_id": 152,
    "name": "Sao Tome and Principe",
    "code": "ST",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tp-flag.gif",
    "calling_code": "+239"
  },
  {
    "country_id": 153,
    "name": "Saudi Arabia",
    "code": "SA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sa-flag.gif",
    "calling_code": "+966"
  },
  {
    "country_id": 154,
    "name": "Senegal",
    "code": "SN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sg-flag.gif",
    "calling_code": "+221"
  },
  {
    "country_id": 155,
    "name": "Serbia",
    "code": "RS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ri-flag.gif",
    "calling_code": "+381"
  },
  {
    "country_id": 156,
    "name": "Seychelles",
    "code": "SC",
    "flag": "https://www.worldometers.info/img/flags/small/tn_se-flag.gif",
    "calling_code": "+248"
  },
  {
    "country_id": 157,
    "name": "Sierra Leone",
    "code": "SL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sl-flag.gif",
    "calling_code": "+232"
  },
  {
    "country_id": 158,
    "name": "Singapore",
    "code": "SG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sn-flag.gif",
    "calling_code": "+65"
  },
  {
    "country_id": 159,
    "name": "Slovakia",
    "code": "SK",
    "flag": "https://www.worldometers.info/img/flags/small/tn_lo-flag.gif",
    "calling_code": "+421"
  },
  {
    "country_id": 160,
    "name": "Slovenia",
    "code": "SI",
    "flag": "https://www.worldometers.info/img/flags/small/tn_si-flag.gif",
    "calling_code": "+386"
  },
  {
    "country_id": 161,
    "name": "Solomon Islands",
    "code": "SB",
    "flag": "https://www.worldometers.info/img/flags/small/tn_bp-flag.gif",
    "calling_code": "+677"
  },
  {
    "country_id": 162,
    "name": "Somalia",
    "code": "SO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_so-flag.gif",
    "calling_code": "+252"
  },
  {
    "country_id": 163,
    "name": "South Africa",
    "code": "ZA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sf-flag.gif",
    "calling_code": "+27"
  },
  {
    "country_id": 164,
    "name": "South Sudan",
    "code": "SS",
    "flag": "https://www.worldometers.info/img/flags/small/tn_od-flag.gif",
    "calling_code": "+211"
  },
  {
    "country_id": 165,
    "name": "Spain",
    "code": "ES",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sp-flag.gif",
    "calling_code": "+34"
  },
  {
    "country_id": 166,
    "name": "Sri Lanka",
    "code": "LK",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ce-flag.gif",
    "calling_code": "+94"
  },
  {
    "country_id": 167,
    "name": "Sudan (the)",
    "code": "SD",
    "flag": "https://www.worldometers.info/img/flags/small/tn_su-flag.gif",
    "calling_code": "+249"
  },
  {
    "country_id": 168,
    "name": "Suriname",
    "code": "SR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ns-flag.gif",
    "calling_code": "+597"
  },
  {
    "country_id": 169,
    "name": "Sweden",
    "code": "SE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sw-flag.gif",
    "calling_code": "+46"
  },
  {
    "country_id": 170,
    "name": "Switzerland",
    "code": "CH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sz-flag.gif",
    "calling_code": "+41"
  },
  {
    "country_id": 171,
    "name": "Syrian Arab Republic",
    "code": "SY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_sy-flag.gif",
    "calling_code": "+963"
  },
  {
    "country_id": 172,
    "name": "Tajikistan",
    "code": "TJ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ti-flag.gif",
    "calling_code": "+992"
  },
  {
    "country_id": 173,
    "name": "Tanzania, United Republic of",
    "code": "TZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tz-flag.gif",
    "calling_code": "+255"
  },
  {
    "country_id": 174,
    "name": "Thailand",
    "code": "TH",
    "flag": "https://www.worldometers.info/img/flags/small/tn_th-flag.gif",
    "calling_code": "+66"
  },
  {
    "country_id": 175,
    "name": "Timor-Leste",
    "code": "TL",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tt-flag.gif",
    "calling_code": "+670"
  },
  {
    "country_id": 176,
    "name": "Togo",
    "code": "TG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_to-flag.gif",
    "calling_code": "+228"
  },
  {
    "country_id": 177,
    "name": "Tonga",
    "code": "TO",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tn-flag.gif",
    "calling_code": "+676"
  },
  {
    "country_id": 178,
    "name": "Trinidad and Tobago",
    "code": "TT",
    "flag": "https://www.worldometers.info/img/flags/small/tn_td-flag.gif",
    "calling_code": "+1 (868)"
  },
  {
    "country_id": 179,
    "name": "Tunisia",
    "code": "TN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ts-flag.gif",
    "calling_code": "+216"
  },
  {
    "country_id": 180,
    "name": "Turkey",
    "code": "TR",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tu-flag.gif",
    "calling_code": "+90"
  },
  {
    "country_id": 181,
    "name": "Turkmenistan",
    "code": "TM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tx-flag.gif",
    "calling_code": "+993"
  },
  {
    "country_id": 182,
    "name": "Tuvalu",
    "code": "TV",
    "flag": "https://www.worldometers.info/img/flags/small/tn_tv-flag.gif",
    "calling_code": "+688"
  },
  {
    "country_id": 183,
    "name": "Uganda",
    "code": "UG",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ug-flag.gif",
    "calling_code": "+256"
  },
  {
    "country_id": 184,
    "name": "Ukraine",
    "code": "UA",
    "flag": "https://www.worldometers.info/img/flags/small/tn_up-flag.gif",
    "calling_code": "+380"
  },
  {
    "country_id": 185,
    "name": "United Arab Emirates (the)",
    "code": "AE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ae-flag.gif",
    "calling_code": "+971"
  },
  {
    "country_id": 186,
    "name": "United Kingdom of Great Britain and Northern Ireland (the)",
    "code": "GB",
    "flag": "https://www.worldometers.info/img/flags/small/tn_uk-flag.gif",
    "calling_code": "+44"
  },
  {
    "country_id": 187,
    "name": "United States of America (the)",
    "code": "US",
    "flag": "https://www.worldometers.info/img/flags/small/tn_us-flag.gif",
    "calling_code": "+1"
  },
  {
    "country_id": 188,
    "name": "Uruguay",
    "code": "UY",
    "flag": "https://www.worldometers.info/img/flags/small/tn_uy-flag.gif",
    "calling_code": "+598"
  },
  {
    "country_id": 189,
    "name": "Uzbekistan",
    "code": "UZ",
    "flag": "https://www.worldometers.info/img/flags/small/tn_uz-flag.gif",
    "calling_code": "+998"
  },
  {
    "country_id": 190,
    "name": "Vanuatu",
    "code": "VU",
    "flag": "https://www.worldometers.info/img/flags/small/tn_nh-flag.gif",
    "calling_code": "+678"
  },
  {
    "country_id": 191,
    "name": "Venezuela (Bolivarian Republic of)",
    "code": "VE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ve-flag.gif",
    "calling_code": "+58"
  },
  {
    "country_id": 192,
    "name": "Viet Nam",
    "code": "VN",
    "flag": "https://www.worldometers.info/img/flags/small/tn_vm-flag.gif",
    "calling_code": "+84"
  },
  {
    "country_id": 193,
    "name": "Yemen",
    "code": "YE",
    "flag": "https://www.worldometers.info/img/flags/small/tn_ym-flag.gif",
    "calling_code": "+967"
  },
  {
    "country_id": 194,
    "name": "Zambia",
    "code": "ZM",
    "flag": "https://www.worldometers.info/img/flags/small/tn_za-flag.gif",
    "calling_code": "+260"
  },
  {
    "country_id": 195,
    "name": "Zimbabwe",
    "code": "ZW",
    "flag": "https://www.worldometers.info/img/flags/small/tn_zi-flag.gif",
    "calling_code": "+263"
  }
]
)
    op.create_index(op.f('ix_countries_code'), 'countries', ['code'], unique=True)
    op.create_table('courses',
    sa.Column('course_id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('course_id')
    )
    op.create_table('lessons',
    sa.Column('lesson_id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('body_md', sa.Text(), nullable=True),
    sa.Column('video_url', sa.String(), nullable=True),
    sa.Column('file_url', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('lesson_id')
    )
    op.create_table('course_contents',
    sa.Column('content_id', sa.UUID(), nullable=False),
    sa.Column('course_id', sa.UUID(), nullable=False),
    sa.Column('content_type', sa.Enum('TEXT', 'VIDEO', 'ASSESSMENT', name='coursecontenttype'), nullable=False),
    sa.Column('lesson_id', sa.UUID(), nullable=True),
    sa.Column('assessment_id', sa.UUID(), nullable=True),
    sa.Column('sequence', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['assessment_id'], ['assessments.assessment_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['course_id'], ['courses.course_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['lesson_id'], ['lessons.lesson_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('content_id')
    )
    op.create_table('dimensions',
    sa.Column('dimension_id', sa.UUID(), nullable=False),
    sa.Column('assessment_id', sa.UUID(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('sequence', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['assessment_id'], ['assessments.assessment_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('dimension_id')
    )
    op.create_table('result_profiles',
    sa.Column('profile_id', sa.UUID(), nullable=False),
    sa.Column('assessment_id', sa.UUID(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('criteria_json', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['assessment_id'], ['assessments.assessment_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('profile_id')
    )
    op.create_table('users',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('user_name', sa.String(), nullable=True),
    sa.Column('profile_picture', sa.String(), nullable=True),
    sa.Column('date_of_birth', sa.DateTime(timezone=True), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('gender', sa.Enum('MALE', 'FEMALE', 'OTHER', name='usergender'), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_banned', sa.Boolean(), nullable=False),
    sa.Column('roles', sa.ARRAY(sa.Enum('USER', 'PARENT', 'ADMIN', name='userrole')), nullable=False),
    sa.ForeignKeyConstraint(['country_id'], ['countries.country_id'], name='users_country_id_fky', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('user_id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('user_name')
    )
    op.create_table('contact_us_submission',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('message', sa.String(), nullable=False),
    sa.Column('is_read', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], name='user_auth_users_fky', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('questions',
    sa.Column('question_id', sa.UUID(), nullable=False),
    sa.Column('assessment_id', sa.UUID(), nullable=False),
    sa.Column('dimension_id', sa.UUID(), nullable=False),
    sa.Column('body_md', sa.Text(), nullable=False),
    sa.Column('sequence', sa.Integer(), nullable=True),
    sa.Column('block_label', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['assessment_id'], ['assessments.assessment_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['dimension_id'], ['dimensions.dimension_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('question_id')
    )
    op.create_table('refresh_tokens',
    sa.Column('jwt_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('device_type', sa.String(), nullable=False),
    sa.Column('hash_refresh_token', sa.String(), nullable=False),
    sa.Column('refresh_token_exp', sa.DateTime(timezone=True), nullable=True),
    sa.Column('public_ip', sa.String(), nullable=False),
    sa.Column('is_blackList', sa.Boolean(), nullable=False),
    sa.Column('issued_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], name='user_auth_users_fky', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('jwt_id')
    )
    op.create_index(op.f('ix_refresh_tokens_hash_refresh_token'), 'refresh_tokens', ['hash_refresh_token'], unique=False)
    op.create_index('ix_rt_user_id', 'refresh_tokens', ['user_id'], unique=False)
    op.create_table('user_auth',
    sa.Column('uid', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('auth_provider', sa.Enum('LOCAL', 'GOOGLE', 'FACEBOOK', name='authprovider'), nullable=False),
    sa.Column('hashed_password', sa.String(), nullable=True),
    sa.Column('verification_code', sa.String(), nullable=True),
    sa.Column('verification_code_exp', sa.DateTime(timezone=True), nullable=True),
    sa.Column('email_confirmed', sa.Boolean(), nullable=False),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('lockout_until', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], name='user_auth_users_fky', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uid'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('user_sessions',
    sa.Column('session_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('assessment_id', sa.UUID(), nullable=False),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['assessment_id'], ['assessments.assessment_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], ),
    sa.PrimaryKeyConstraint('session_id')
    )
    op.create_table('whatsapp_sessions',
    sa.Column('_id', sa.UUID(), nullable=False),
    sa.Column('session_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('phone_number', sa.String(), nullable=False),
    sa.Column('qr_code', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], name='user_auth_users_fky', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('_id'),
    sa.UniqueConstraint('session_id')
    )
    op.create_table('options',
    sa.Column('option_id', sa.UUID(), nullable=False),
    sa.Column('question_id', sa.UUID(), nullable=False),
    sa.Column('label', sa.String(), nullable=False),
    sa.Column('value', sa.String(), nullable=True),
    sa.Column('is_correct', sa.Boolean(), nullable=True),
    sa.Column('comment', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['question_id'], ['questions.question_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('option_id')
    )
    op.create_table('session_profile',
    sa.Column('session_id', sa.UUID(), nullable=False),
    sa.Column('profile_id', sa.UUID(), nullable=False),
    sa.Column('confidence_pct', sa.Numeric(), nullable=True),
    sa.ForeignKeyConstraint(['profile_id'], ['result_profiles.profile_id'], ),
    sa.ForeignKeyConstraint(['session_id'], ['user_sessions.session_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('session_id')
    )
    op.create_table('option_scores',
    sa.Column('option_id', sa.UUID(), nullable=False),
    sa.Column('dimension_id', sa.UUID(), nullable=False),
    sa.Column('delta', sa.Numeric(), nullable=False),
    sa.Column('is_correct', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['dimension_id'], ['dimensions.dimension_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['option_id'], ['options.option_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('option_id', 'dimension_id')
    )
    op.create_table('responses',
    sa.Column('response_id', sa.UUID(), nullable=False),
    sa.Column('session_id', sa.UUID(), nullable=False),
    sa.Column('question_id', sa.UUID(), nullable=False),
    sa.Column('option_id', sa.UUID(), nullable=True),
    sa.Column('value', sa.String(), nullable=True),
    sa.Column('answered_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['option_id'], ['options.option_id'], ),
    sa.ForeignKeyConstraint(['question_id'], ['questions.question_id'], ),
    sa.ForeignKeyConstraint(['session_id'], ['user_sessions.session_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('response_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('responses')
    op.drop_table('option_scores')
    op.drop_table('session_profile')
    op.drop_table('options')
    op.drop_table('whatsapp_sessions')
    op.drop_table('user_sessions')
    op.drop_table('user_auth')
    op.drop_index('ix_rt_user_id', table_name='refresh_tokens')
    op.drop_index(op.f('ix_refresh_tokens_hash_refresh_token'), table_name='refresh_tokens')
    op.drop_table('refresh_tokens')
    op.drop_table('questions')
    op.drop_table('contact_us_submission')
    op.drop_table('users')
    op.drop_table('result_profiles')
    op.drop_table('dimensions')
    op.drop_table('course_contents')
    op.drop_table('lessons')
    op.drop_table('courses')
    op.drop_index(op.f('ix_countries_code'), table_name='countries')
    op.drop_table('countries')
    op.drop_table('assessments')
    # ### end Alembic commands ###
