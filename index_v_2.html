<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Assessment Manager</title>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.23.4/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      @keyframes slideIn {
        from {
          transform: translateY(-20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      .fade-in {
        animation: fadeIn 0.3s ease-out;
      }
      .slide-in {
        animation: slideIn 0.3s ease-out;
      }
      .hover-scale {
        transition: transform 0.2s ease;
      }
      .hover-scale:hover {
        transform: scale(1.05);
      }
      .modal-overlay {
        backdrop-filter: blur(8px);
        background: rgba(0, 0, 0, 0.6);
      }
      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4f46e5;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .option-label input:checked + span {
        background: #dbeafe;
        font-weight: 500;
        border-radius: 8px;
        padding: 8px;
      }
      .tab-active {
        border-bottom: 2px solid #4f46e5;
        color: #4f46e5;
        font-weight: 600;
      }
      .unanswered-question {
        border-left: 4px solid #ef4444;
        background-color: #fef2f2;
        padding-left: 12px;
      }
      .missing-answer {
        color: #ef4444;
        font-weight: 500;
      }
      .warning-icon {
        color: #ef4444;
        margin-right: 4px;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen font-sans">
    <div id="root"></div>

    <script type="text/babel">
      const API_BASE_URL = "http://127.0.0.1:8099";
      const AUTH_URL = "http://127.0.0.1:8099/login";

      const ASSESSMENT_TEMPLATE = {
        code: "MOCK", // default to something valid
        name: "Untitled Assessment",
        version_int: 1,
        dimensions: [
          { dimension_id: null, name: "TRUE_FALSE", code: "TF", sequence: 1 },
          {
            dimension_id: null,
            name: "SINGLE_CHOICE",
            code: "SC",
            sequence: 2,
          },
          {
            dimension_id: null,
            name: "MULTIPLE_CHOICE",
            code: "MC",
            sequence: 3,
          },
        ],
        questions: [],
      };
      const COURSE_TEMPLATE = {
        title: "",
        description: "",
        is_active: true,
        lessons: [],
      };
      const LESSON_TEMPLATE = {
        title: "",
        body_md: "",
        video_url: "",
        file_url: "",
        assessment_id: null,
      };

      const ASSESSMENT_TYPES = [
        "MBTI",
        "HEXACO",
        "PF16",
        "DISC",
        "RORSCHACH",
        "KEIRSEY",
      ];
      const DIMENSION_TYPES = [
        "TRUE_FALSE",
        "SINGLE_CHOICE",
        "MULTIPLE_CHOICE",
      ];
      const DIMENSION_CODES = ["TF", "SC", "MC"];
      const CONTENT_TYPES = ["TEXT", "VIDEO", "ASSESSMENT"];

      // Axios interceptor to attach Bearer token
      axios.interceptors.request.use(
        (config) => {
          const token = localStorage.getItem("access_token");
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        },
        (error) => Promise.reject(error)
      );

      // Handle 401 unauthorized responses
      axios.interceptors.response.use(
        (response) => response,
        (error) => {
          if (error.response?.status === 401) {
            localStorage.removeItem("access_token");
            window.location.reload();
          }
          return Promise.reject(error);
        }
      );

      const LoginModal = ({ onLogin }) => {
        const [username, setUsername] = React.useState("");
        const [password, setPassword] = React.useState("");
        const [isLoading, setIsLoading] = React.useState(false);
        const [error, setError] = React.useState("");

        const handleSubmit = async (e) => {
          e.preventDefault();
          setIsLoading(true);
          setError("");
          try {
            const formData = new URLSearchParams();
            formData.append("grant_type", "password");
            formData.append("username", username);
            formData.append("password", password);
            formData.append("scope", "");
            formData.append("client_id", "string");
            formData.append("client_secret", "string");

            const response = await axios.post(AUTH_URL, formData, {
              headers: { "Content-Type": "application/x-www-form-urlencoded" },
            });
            localStorage.setItem("access_token", response.data.access_token);
            setUsername("");
            setPassword("");
            onLogin();
          } catch (err) {
            setError(
              err.response?.data?.detail || "Login failed. Please try again."
            );
          } finally {
            setIsLoading(false);
          }
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-md w-full shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">Login</h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Username
                  </label>
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                    disabled={isLoading}
                    required
                  />
                </div>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                    disabled={isLoading}
                    required
                  />
                </div>
                {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center ${
                    isLoading ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {isLoading ? (
                    <div className="spinner mr-2"></div>
                  ) : (
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M11 16l-4-4m0 0l4-4m-4 4h14"
                      />
                    </svg>
                  )}
                  Login
                </button>
              </form>
            </div>
          </div>
        );
      };

      const Notification = ({ message, type, onClose, details }) => (
        <div
          className={`slide-in fixed top-4 right-4 p-4 rounded-lg shadow-lg text-white ${
            type === "success" ? "bg-green-500" : "bg-red-500"
          } flex flex-col max-w-md`}
        >
          <div className="flex justify-between items-start">
            <span>{message}</span>
            <button
              onClick={onClose}
              className="ml-4 text-white hover:text-gray-200"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          {details && (
            <div className="mt-2 text-sm bg-white bg-opacity-20 p-2 rounded">
              {Array.isArray(details) ? (
                <ul className="list-disc pl-5">
                  {details.map((detail, i) => (
                    <li key={i}>{detail}</li>
                  ))}
                </ul>
              ) : (
                <p>{details}</p>
              )}
            </div>
          )}
        </div>
      );

      const AssessmentCard = ({
        assessment,
        onEdit,
        onDelete,
        onViewAsStudent,
        onStartSession,
      }) => (
        <div className="fade-in bg-white rounded-xl shadow-xl p-6 mb-6 hover-scale border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">
            {assessment.name}
          </h3>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Code:</span> {assessment.code}
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Version:</span>{" "}
            {assessment.version_int}
          </p>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => onEdit(assessment)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit
            </button>
            <button
              onClick={() => onDelete(assessment.assessment_id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4m-4 0h4m-8 4h12"
                />
              </svg>
              Delete
            </button>
            <button
              onClick={() =>
                onViewAsStudent(assessment.assessment_id, assessment.name)
              }
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm6 0c0 3.18-1.35 6.06-3.52 8.07a16.84 16.84 0 01-11.96 0C3.35 18.06 2 15.18 2 12s1.35-6.06 3.52-8.07a16.84 16.84 0 0111.96 0C19.65 5.94 21 8.82 21 12z"
                />
              </svg>
              View as Student
            </button>
            <button
              onClick={() =>
                onStartSession(assessment.assessment_id, assessment.name)
              }
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 2m6-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Start Session
            </button>
          </div>
        </div>
      );

      const SessionCard = ({
        session,
        onView,
        onDelete,
        onSubmitResponses,
        onViewResults,
      }) => (
        <div className="fade-in bg-white rounded-xl shadow-xl p-6 mb-6 hover-scale border border-gray-100">
          <h3 className="text-xl font-bold text-gray-800 mb-2">
            Session ID: {session.session_id}
          </h3>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Assessment ID:</span>{" "}
            {session.assessment_id}
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Started:</span>{" "}
            {new Date(session.started_at).toLocaleString()}
          </p>
          <p className="text-gray-600 mb-4">
            <span className="font-semibold">Completed:</span>{" "}
            {session.completed_at
              ? new Date(session.completed_at).toLocaleString()
              : "Not completed"}
          </p>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => onView(session.session_id)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm6 0c0 3.18-1.35 6.06-3.52 8.07a16.84 16.84 0 01-11.96 0C3.35 18.06 2 15.18 2 12s1.35-6.06 3.52-8.07a16.84 16.84 0 0111.96 0C19.65 5.94 21 8.82 21 12z"
                />
              </svg>
              View
            </button>
            <button
              onClick={() => onDelete(session.session_id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4m-4 0h4m-8 4h12"
                />
              </svg>
              Delete
            </button>
            {!session.completed_at && (
              <button
                onClick={() =>
                  onSubmitResponses(session.session_id, session.assessment_id)
                }
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 3"
                  />
                </svg>
                Submit Responses
              </button>
            )}
            {session.completed_at && (
              <button
                onClick={() => onViewResults(session.session_id)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2-12H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2z"
                  />
                </svg>
                View Results
              </button>
            )}
          </div>
        </div>
      );

      const CourseCard = ({ course, onEdit, onDelete }) => (
        <div className="fade-in bg-white rounded-xl shadow-xl p-6 mb-6 hover-scale border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">
            {course.title}
          </h3>
          <p className="text-gray-600 mb-2">
            {course.description || "No description provided"}
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Status:</span>
            <span
              className={`ml-2 px-2 py-1 rounded-full text-xs ${
                course.is_active
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
              }`}
            >
              {course.is_active ? "Active" : "Inactive"}
            </span>
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Created:</span>{" "}
            {new Date(course.created_at).toLocaleDateString()}
          </p>
          <p className="text-gray-600 mb-4">
            <span className="font-semibold">Content Items:</span>{" "}
            {course.contents?.length || 0}
          </p>

          {course.contents && course.contents.length > 0 && (
            <div className="mb-4">
              <h4 className="font-semibold text-gray-700 mb-2">
                Course Content:
              </h4>
              <div className="space-y-1">
                {course.contents.slice(0, 3).map((content, index) => (
                  <div
                    key={content.content_id}
                    className="text-sm text-gray-600 flex items-center"
                  >
                    <span className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs mr-2">
                      {content.sequence + 1}
                    </span>
                    <span className="capitalize">
                      {content.content_type.toLowerCase()}
                    </span>
                    {content.lesson && (
                      <span className="ml-2">- {content.lesson.title}</span>
                    )}
                    {content.assessment && (
                      <span className="ml-2">- {content.assessment.name}</span>
                    )}
                  </div>
                ))}
                {course.contents.length > 3 && (
                  <div className="text-sm text-gray-500">
                    ... and {course.contents.length - 3} more items
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => onEdit(course)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit
            </button>
            <button
              onClick={() => onDelete(course.course_id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4m-4 0h4m-8 4h12"
                />
              </svg>
              Delete
            </button>
          </div>
        </div>
      );

      const SessionViewModal = ({ sessionId, onClose, setNotification }) => {
        const [session, setSession] = React.useState(null);
        const [responses, setResponses] = React.useState([]);
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchSessionData = async () => {
            setIsLoading(true);
            try {
              const [sessionRes, responsesRes] = await Promise.all([
                axios.get(`${API_BASE_URL}/sessions/${sessionId}`),
                axios.get(`${API_BASE_URL}/sessions/${sessionId}/response`),
              ]);
              setSession(sessionRes.data);
              setResponses(responsesRes.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to load session data",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchSessionData();
        }, [sessionId]);

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Session Details
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : !session ? (
                <p className="text-gray-600">No session data available.</p>
              ) : (
                <div className="space-y-6">
                  <div>
                    <p className="text-gray-600">
                      <span className="font-semibold">Session ID:</span>{" "}
                      {session.session_id}
                    </p>
                    <p className="text-gray-600">
                      <span className="font-semibold">Assessment ID:</span>{" "}
                      {session.assessment_id}
                    </p>
                    <p className="text-gray-600">
                      <span className="font-semibold">Started:</span>{" "}
                      {new Date(session.started_at).toLocaleString()}
                    </p>
                    <p className="text-gray-600">
                      <span className="font-semibold">Completed:</span>{" "}
                      {session.completed_at
                        ? new Date(session.completed_at).toLocaleString()
                        : "Not completed"}
                    </p>
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-gray-800">
                    Responses
                  </h3>
                  {responses.length === 0 ? (
                    <p className="text-gray-600">No responses submitted.</p>
                  ) : (
                    responses.map((response, index) => (
                      <div
                        key={response.response_id}
                        className={`p-4 rounded-lg border mb-4 ${
                          response.option_id
                            ? "bg-gray-50 border-gray-200"
                            : "unanswered-question border-red-200"
                        }`}
                      >
                        <p className="text-gray-600">
                          <span className="font-semibold">
                            Question {index + 1} ID:
                          </span>{" "}
                          {response.question_id}
                        </p>
                        {response.option_id ? (
                          <>
                            <p className="text-gray-600">
                              <span className="font-semibold">Option ID:</span>{" "}
                              {response.option_id}
                            </p>
                            {response.value && (
                              <p className="text-gray-600">
                                <span className="font-semibold">Value:</span>{" "}
                                {response.value}
                              </p>
                            )}
                          </>
                        ) : (
                          <p className="missing-answer">
                            <span className="warning-icon">⚠️</span> No answer
                            provided
                          </p>
                        )}
                        <p className="text-gray-600">
                          <span className="font-semibold">Answered At:</span>{" "}
                          {new Date(response.answered_at).toLocaleString()}
                        </p>
                      </div>
                    ))
                  )}
                </div>
              )}
              <div className="mt-6 flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Close
                </button>
              </div>
            </div>
          </div>
        );
      };

      const SessionResultsModal = ({ sessionId, onClose, setNotification }) => {
        const [result, setResult] = React.useState(null);
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchResult = async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(
                `${API_BASE_URL}/sessions/${sessionId}/profile-result`
              );
              setResult(response.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail ||
                  "Failed to load session results",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchResult();
        }, [sessionId]);

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Assessment Results
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : !result ? (
                <p className="text-gray-600">No results available yet.</p>
              ) : (
                <div className="space-y-6">
                  <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                    <h3 className="text-2xl font-semibold text-blue-800 mb-2">
                      Your Personality Type: {result.profile.code} -{" "}
                      {result.profile.name}
                    </h3>
                    <p className="text-gray-700 mb-4">
                      {result.profile.description}
                    </p>
                    {result.confidence_pct && (
                      <div className="w-full bg-gray-200 rounded-full h-4">
                        <div
                          className="bg-blue-600 h-4 rounded-full"
                          style={{
                            width: `${(result.confidence_pct * 100).toFixed(
                              0
                            )}%`,
                          }}
                        ></div>
                      </div>
                    )}
                    <p className="text-sm text-gray-600 mt-2">
                      Confidence:{" "}
                      {result.confidence_pct
                        ? `${(result.confidence_pct * 100).toFixed(2)}%`
                        : "N/A"}
                    </p>
                  </div>

                  {result.profile.criteria_json && (
                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <h4 className="text-lg font-medium text-gray-800 mb-3">
                        Detailed Analysis
                      </h4>
                      <div className="space-y-4">
                        {Object.entries(result.profile.criteria_json).map(
                          ([dimension, score]) => (
                            <div key={dimension} className="mb-3">
                              <div className="flex justify-between mb-1">
                                <span className="text-sm font-medium text-gray-700">
                                  {dimension}
                                </span>
                                <span className="text-sm font-medium text-gray-700">
                                  {typeof score === "number"
                                    ? `${(score * 100).toFixed(0)}%`
                                    : score}
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  className="bg-indigo-600 h-2.5 rounded-full"
                                  style={{
                                    width:
                                      typeof score === "number"
                                        ? `${(score * 100).toFixed(0)}%`
                                        : "100%",
                                  }}
                                ></div>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="mt-6 flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Close
                </button>
              </div>
            </div>
          </div>
        );
      };

      const SessionResponseModal = ({
        sessionId,
        assessmentId,
        onClose,
        setNotification,
      }) => {
        const [questions, setQuestions] = React.useState([]);
        const [responses, setResponses] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchQuestions = async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(
                `${API_BASE_URL}/${assessmentId}/questions`
              );
              setQuestions(response.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to load questions",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchQuestions();
        }, [assessmentId]);

        const handleResponseChange = (questionId, optionId) => {
          setResponses({ ...responses, [questionId]: optionId });
        };

        const handleSubmit = async () => {
          const totalQuestions = questions.length;
          const answeredQuestions = Object.keys(responses).length;

          if (answeredQuestions !== totalQuestions) {
            const unansweredQuestions = questions.filter(
              (q) => !responses[q.question_id]
            );
            setNotification({
              message: `Please answer all questions before submitting. Missing answers for ${unansweredQuestions.length} questions.`,
              type: "error",
              details: unansweredQuestions.map((q) => q.body_md),
            });
            return;
          }

          setIsLoading(true);
          try {
            const responseData = questions.map((question) => {
              const selectedOption = question.options.find(
                (opt) => opt.option_id === responses[question.question_id]
              );
              return {
                question_id: question.question_id,
                option_id: responses[question.question_id],
                value: selectedOption ? selectedOption.value : null,
                answered_at: new Date().toISOString(),
              };
            });

            const response = await axios.post(
              `${API_BASE_URL}/sessions/${sessionId}/submit`,
              responseData
            );

            setNotification({
              message: "Responses submitted successfully! Results are ready.",
              type: "success",
            });
            onClose();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to submit responses",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Assessment Questions
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : questions.length === 0 ? (
                <p className="text-gray-600">
                  No questions available for this assessment.
                </p>
              ) : (
                <div className="space-y-6">
                  {questions.map((question, index) => (
                    <div
                      key={question.question_id}
                      className={`bg-gray-50 p-4 rounded-lg border ${
                        responses[question.question_id]
                          ? "border-gray-200"
                          : "unanswered-question border-red-200"
                      }`}
                    >
                      <h3 className="text-lg font-medium text-gray-800 mb-3">
                        Question {index + 1}: {question.body_md}
                        {!responses[question.question_id] && (
                          <span className="missing-answer ml-2">
                            <span className="warning-icon">⚠️</span> Not
                            answered
                          </span>
                        )}
                      </h3>
                      <div className="space-y-2">
                        {question.options.map((option) => (
                          <label
                            key={option.option_id}
                            className="option-label flex items-center p-2 rounded-lg cursor-pointer"
                          >
                            <input
                              type="radio"
                              name={`question-${question.question_id}`}
                              value={option.option_id}
                              checked={
                                responses[question.question_id] ===
                                option.option_id
                              }
                              onChange={() =>
                                handleResponseChange(
                                  question.question_id,
                                  option.option_id
                                )
                              }
                              className="mr-2"
                            />
                            <span className="flex-1">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="spinner mr-2"></div>
                  ) : (
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 3"
                      />
                    </svg>
                  )}
                  Submit
                </button>
              </div>
            </div>
          </div>
        );
      };

      const StudentViewModal = ({
        assessmentId,
        assessmentName,
        onClose,
        setNotification,
      }) => {
        const [questions, setQuestions] = React.useState([]);
        const [responses, setResponses] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchQuestions = async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(
                `${API_BASE_URL}/${assessmentId}/questions`
              );
              setQuestions(response.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to load questions",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchQuestions();
        }, [assessmentId]);

        const handleResponseChange = (questionId, optionId) => {
          setResponses({ ...responses, [questionId]: optionId });
        };

        const handleSubmit = () => {
          const totalQuestions = questions.length;
          const answeredQuestions = Object.keys(responses).length;

          if (answeredQuestions !== totalQuestions) {
            const unansweredQuestions = questions.filter(
              (q) => !responses[q.question_id]
            );
            setNotification({
              message: `Please answer all questions before submitting. Missing answers for ${unansweredQuestions.length} questions.`,
              type: "error",
              details: unansweredQuestions.map((q) => q.body_md),
            });
            return;
          }

          const responseDetails = questions.map((q) => ({
            question_id: q.question_id,
            option_id: responses[q.question_id],
            option_text:
              q.options.find((o) => o.option_id === responses[q.question_id])
                ?.label || "",
          }));
          setNotification({
            message: `Assessment preview submitted!`,
            details: responseDetails,
            type: "success",
          });
          onClose();
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Assessment: {assessmentName}
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : questions.length === 0 ? (
                <p className="text-gray-600">
                  No questions available for this assessment.
                </p>
              ) : (
                <div className="space-y-6">
                  {questions.map((question, index) => (
                    <div
                      key={question.question_id}
                      className={`bg-gray-50 p-4 rounded-lg border ${
                        responses[question.question_id]
                          ? "border-gray-200"
                          : "unanswered-question border-red-200"
                      }`}
                    >
                      <h3 className="text-lg font-medium text-gray-800 mb-3">
                        Question {index + 1}: {question.body_md}
                        {!responses[question.question_id] && (
                          <span className="missing-answer ml-2">
                            <span className="warning-icon">⚠️</span> Not
                            answered
                          </span>
                        )}
                      </h3>
                      <div className="space-y-2">
                        {question.options.map((option) => (
                          <label
                            key={option.option_id}
                            className="option-label flex items-center p-2 rounded-lg cursor-pointer"
                          >
                            <input
                              type="radio"
                              name={`question-${question.question_id}`}
                              value={option.option_id}
                              checked={
                                responses[question.question_id] ===
                                option.option_id
                              }
                              onChange={() =>
                                handleResponseChange(
                                  question.question_id,
                                  option.option_id
                                )
                              }
                              className="mr-2"
                            />
                            <span className="flex-1">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Close
                </button>
                <button
                  onClick={handleSubmit}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 3"
                    />
                  </svg>
                  Submit
                </button>
              </div>
            </div>
          </div>
        );
      };

      const AssessmentForm = ({ assessment, onSave, onCancel }) => {
        const [formData, setFormData] = React.useState(
          assessment || ASSESSMENT_TEMPLATE
        );
        const [errors, setErrors] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);
        const [removedDimensions, setRemovedDimensions] = React.useState([]);
        const [removedQuestions, setRemovedQuestions] = React.useState([]);
        const [removedOptions, setRemovedOptions] = React.useState([]);

        const validateForm = () => {
          const newErrors = {};
          if (!formData.name) newErrors.name = "Name is required";
          if (!formData.code) newErrors.code = "Code is required";
          if (formData.version_int === undefined || formData.version_int < 0)
            newErrors.version_int = "Version must be non-negative";
          formData.dimensions.forEach((dim, index) => {
            if (!dim.name)
              newErrors[`dimension_name_${index}`] =
                "Dimension name is required";
            if (!dim.code)
              newErrors[`dimension_code_${index}`] =
                "Dimension code is required";
            if (dim.sequence < 0)
              newErrors[`dimension_sequence_${index}`] =
                "Sequence must be non-negative";
          });
          formData.questions.forEach((que, index) => {
            if (!que.body_md)
              newErrors[`question_body_${index}`] = "Question body is required";
            if (!que.dimension_code)
              newErrors[`question_dimension_${index}`] =
                "Dimension code is required";
            if (que.sequence < 0)
              newErrors[`question_sequence_${index}`] =
                "Sequence must be non-negative";
            que.options.forEach((opt, oIndex) => {
              if (!opt.label)
                newErrors[`option_label_${index}_${oIndex}`] =
                  "Option label is required";
            });
          });
          setErrors(newErrors);
          return Object.keys(newErrors).length === 0;
        };

        const handleChange = (e) => {
          const { name, value } = e.target;
          setFormData({
            ...formData,
            [name]: name === "version_int" ? parseInt(value) : value,
          });
          setErrors({ ...errors, [name]: null });
        };

        const handleDimensionChange = (index, field, value) => {
          const newDimensions = [...formData.dimensions];
          newDimensions[index][field] =
            field === "sequence" ? parseInt(value) : value;
          setFormData({ ...formData, dimensions: newDimensions });
          setErrors({ ...errors, [`dimension_${field}_${index}`]: null });
        };

        const handleQuestionChange = (index, field, value) => {
          const newQuestions = [...formData.questions];
          newQuestions[index][field] =
            field === "sequence" ? parseInt(value) : value;
          setFormData({ ...formData, questions: newQuestions });
          setErrors({ ...errors, [`question_${field}_${index}`]: null });
        };

        const handleOptionChange = (qIndex, oIndex, field, value) => {
          const newQuestions = [...formData.questions];
          newQuestions[qIndex].options[oIndex][field] = value;
          setFormData({ ...formData, questions: newQuestions });
          setErrors({
            ...errors,
            [`option_${field}_${qIndex}_${oIndex}`]: null,
          });
        };

        const addDimension = () => {
          setFormData({
            ...formData,
            dimensions: [
              ...formData.dimensions,
              {
                dimension_id: null,
                name: "",
                code: "",
                sequence: formData.dimensions.length,
              },
            ],
          });
        };

        const removeDimension = (index) => {
          const dimension = formData.dimensions[index];
          if (dimension.dimension_id) {
            setRemovedDimensions([
              ...removedDimensions,
              dimension.dimension_id,
            ]);
          }
          const newDimensions = formData.dimensions.filter(
            (_, i) => i !== index
          );
          setFormData({ ...formData, dimensions: newDimensions });
        };

        const addQuestion = () => {
          setFormData({
            ...formData,
            questions: [
              ...formData.questions,
              {
                question_id: null,
                body_md: "",
                dimension_code: "",
                sequence: formData.questions.length,
                block_label: "",
                options: [{ option_id: null, label: "", value: "" }],
              },
            ],
          });
        };

        const removeQuestion = (index) => {
          const question = formData.questions[index];
          if (question.question_id) {
            setRemovedQuestions([...removedQuestions, question.question_id]);
            const optionsToRemove = question.options
              .filter((opt) => opt.option_id)
              .map((opt) => opt.option_id);
            setRemovedOptions([...removedOptions, ...optionsToRemove]);
          }
          const newQuestions = formData.questions.filter((_, i) => i !== index);
          setFormData({ ...formData, questions: newQuestions });
        };

        const addOption = (qIndex) => {
          const newQuestions = [...formData.questions];
          newQuestions[qIndex].options.push({
            option_id: null,
            label: "",
            value: "",
          });
          setFormData({ ...formData, questions: newQuestions });
        };

        const removeOption = (qIndex, oIndex) => {
          const option = formData.questions[qIndex].options[oIndex];
          if (option.option_id) {
            setRemovedOptions([...removedOptions, option.option_id]);
          }
          const newQuestions = [...formData.questions];
          newQuestions[qIndex].options = newQuestions[qIndex].options.filter(
            (_, i) => i !== oIndex
          );
          setFormData({ ...formData, questions: newQuestions });
        };
        const handleSubmit = async (e) => {
          e.preventDefault();
          if (!validateForm()) return;

          setIsLoading(true);
          try {
            // Only fields backend accepts
            const basePayload = {
              name: formData.name,
              code: formData.code, // expect MOCK / REAL / QUIZ
              version_int: parseInt(formData.version_int),
            };

            if (assessment?.assessment_id) {
              // ===== UPDATE ASSESSMENT =====
              await axios.put(
                `${API_BASE_URL}/assessments/${assessment.assessment_id}`,
                basePayload
              );

              // delete removed entities
              for (const dimensionId of removedDimensions) {
                await axios.delete(`${API_BASE_URL}/dimensions/${dimensionId}`);
              }
              for (const questionId of removedQuestions) {
                await axios.delete(`${API_BASE_URL}/questions/${questionId}`);
              }
              for (const optionId of removedOptions) {
                await axios.delete(`${API_BASE_URL}/options/${optionId}`);
              }

              // upsert dimensions
              for (const dim of formData.dimensions) {
                if (dim.dimension_id) {
                  await axios.put(
                    `${API_BASE_URL}/dimensions/${dim.dimension_id}`,
                    {
                      name: dim.name,
                      code: dim.code,
                      sequence: dim.sequence,
                    }
                  );
                } else {
                  const d = await axios.post(
                    `${API_BASE_URL}/assessments/${assessment.assessment_id}/dimensions`,
                    {
                      name: dim.name,
                      code: dim.code,
                      sequence: dim.sequence,
                    }
                  );
                  dim.dimension_id = d.data.dimension_id;
                }
              }

              // upsert questions + options
              for (const q of formData.questions) {
                const dimension = formData.dimensions.find(
                  (d) => d.code === q.dimension_code
                );
                if (q.question_id) {
                  await axios.put(
                    `${API_BASE_URL}/questions/${q.question_id}`,
                    {
                      body_md: q.body_md,
                      dimension_id: dimension?.dimension_id,
                      sequence: q.sequence,
                      block_label: q.block_label || null,
                    }
                  );
                  for (const opt of q.options) {
                    if (opt.option_id) {
                      await axios.put(
                        `${API_BASE_URL}/options/${opt.option_id}`,
                        {
                          label: opt.label,
                          value: opt.value || null,
                        }
                      );
                    } else {
                      const o = await axios.post(
                        `${API_BASE_URL}/questions/${q.question_id}/options`,
                        {
                          label: opt.label,
                          value: opt.value || null,
                        }
                      );
                      opt.option_id = o.data.option_id;
                    }
                  }
                } else {
                  const qRes = await axios.post(
                    `${API_BASE_URL}/assessments/${assessment.assessment_id}/questions`,
                    {
                      body_md: q.body_md,
                      dimension_id: dimension?.dimension_id,
                      sequence: q.sequence,
                      block_label: q.block_label || null,
                    }
                  );
                  const newQuestionId = qRes.data.question_id;
                  q.question_id = newQuestionId;

                  for (const opt of q.options) {
                    const o = await axios.post(
                      `${API_BASE_URL}/questions/${newQuestionId}/options`,
                      {
                        label: opt.label,
                        value: opt.value || null,
                      }
                    );
                    opt.option_id = o.data.option_id;
                  }
                }
              }
            } else {
              // ===== CREATE ASSESSMENT + children =====
              const assessRes = await axios.post(
                `${API_BASE_URL}/assessments`,
                basePayload
              );
              const newAssessmentId = assessRes.data.assessment_id;

              for (const dim of formData.dimensions) {
                const d = await axios.post(
                  `${API_BASE_URL}/assessments/${newAssessmentId}/dimensions`,
                  {
                    name: dim.name,
                    code: dim.code,
                    sequence: dim.sequence,
                  }
                );
                dim.dimension_id = d.data.dimension_id;
              }

              for (const q of formData.questions) {
                const dimension = formData.dimensions.find(
                  (d) => d.code === q.dimension_code
                );
                const qRes = await axios.post(
                  `${API_BASE_URL}/assessments/${newAssessmentId}/questions`,
                  {
                    body_md: q.body_md,
                    dimension_id: dimension?.dimension_id,
                    sequence: q.sequence,
                    block_label: q.block_label || null,
                  }
                );
                q.question_id = qRes.data.question_id;

                for (const opt of q.options) {
                  const o = await axios.post(
                    `${API_BASE_URL}/questions/${q.question_id}/options`,
                    {
                      label: opt.label,
                      value: opt.value || null,
                    }
                  );
                  opt.option_id = o.data.option_id;
                }
              }
            }

            setRemovedDimensions([]);
            setRemovedQuestions([]);
            setRemovedOptions([]);
            onSave("Assessment saved successfully", "success");
          } catch (error) {
            console.error("Error saving assessment:", error);
            onSave(
              error.response?.data?.detail || "Failed to save assessment",
              "error"
            );
          } finally {
            setIsLoading(false);
          }
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                {assessment ? "Edit Assessment" : "Create Assessment"}
              </h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.name ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                  )}
                </div>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Code
                  </label>
                  <select
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.code ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select Code</option>
                    <option value="MOCK">Mock</option>
                    <option value="REAL">Real</option>
                    <option value="QUIZ">Quiz</option>
                  </select>

                  {errors.code && (
                    <p className="text-red-500 text-sm mt-1">{errors.code}</p>
                  )}
                </div>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Version
                  </label>
                  <input
                    type="number"
                    name="version_int"
                    value={formData.version_int}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.version_int ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.version_int && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.version_int}
                    </p>
                  )}
                </div>

                <h3 className="text-xl font-semibold mb-4 text-gray-800">
                  Dimensions
                </h3>
                {formData.dimensions.map((dim, index) => (
                  <div
                    key={dim.dimension_id || index}
                    className="mb-5 p-5 bg-gray-50 rounded-lg border border-gray-200 relative"
                  >
                    <button
                      type="button"
                      onClick={() => removeDimension(index)}
                      className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                      disabled={isLoading}
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                    <select
                      value={dim.name}
                      onChange={(e) =>
                        handleDimensionChange(index, "name", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-indigo-500 ${
                        errors[`dimension_name_${index}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">Select Dimension Type</option>
                      {DIMENSION_TYPES.map((type) => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>
                    {errors[`dimension_name_${index}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`dimension_name_${index}`]}
                      </p>
                    )}
                    <select
                      value={dim.code}
                      onChange={(e) =>
                        handleDimensionChange(index, "code", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-indigo-500 ${
                        errors[`dimension_code_${index}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">Select Dimension Code</option>
                      {DIMENSION_CODES.map((code) => (
                        <option key={code} value={code}>
                          {code}
                        </option>
                      ))}
                    </select>
                    {errors[`dimension_code_${index}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`dimension_code_${index}`]}
                      </p>
                    )}
                    <input
                      type="number"
                      placeholder="Sequence"
                      value={dim.sequence}
                      onChange={(e) =>
                        handleDimensionChange(index, "sequence", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                        errors[`dimension_sequence_${index}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors[`dimension_sequence_${index}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`dimension_sequence_${index}`]}
                      </p>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addDimension}
                  className="mb-5 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Add Dimension
                </button>
                <h3 className="text-xl font-semibold mb-4 text-gray-700 mb-2">
                  Questions
                </h3>
                {formData.questions.map((que, qIndex) => (
                  <div
                    key={que.question_id || qIndex}
                    className="mb-5 p-5 bg-gray-50 rounded-lg border border-gray-200 relative"
                  >
                    <button
                      type="button"
                      onClick={() => removeQuestion(qIndex)}
                      className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                      disabled={isLoading}
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                    <input
                      type="text"
                      placeholder="Question Body (Markdown)"
                      value={que.body_md}
                      onChange={(e) =>
                        handleQuestionChange(qIndex, "body_md", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-blue-500 ${
                        errors[`question_body_${qIndex}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors[`question_body_${qIndex}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`question_body_${qIndex}`]}
                      </p>
                    )}
                    <select
                      value={que.dimension_code}
                      onChange={(e) =>
                        handleQuestionChange(
                          qIndex,
                          "dimension_code",
                          e.target.value
                        )
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-blue-500 ${
                        errors[`question_dimension_${qIndex}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">Select Dimension Code</option>
                      {formData.dimensions.map((dim) => (
                        <option key={dim.code} value={dim.code}>
                          {dim.code}
                        </option>
                      ))}
                    </select>
                    {errors[`question_dimension_${qIndex}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`question_dimension_${qIndex}`]}
                      </p>
                    )}
                    <input
                      type="number"
                      placeholder="Sequence"
                      value={que.sequence}
                      onChange={(e) =>
                        handleQuestionChange(qIndex, "sequence", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-blue-500 ${
                        errors[`question_sequence_${qIndex}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors[`question_sequence_${qIndex}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`question_sequence_${qIndex}`]}
                      </p>
                    )}
                    <input
                      type="text"
                      placeholder="Block Label (Optional)"
                      value={que.block_label || ""}
                      onChange={(e) =>
                        handleQuestionChange(
                          qIndex,
                          "block_label",
                          e.target.value
                        )
                      }
                      className="w-full p-3 border rounded-lg mb-4 focus:ring-2 focus:ring-blue-500 border-gray-300"
                      disabled={isLoading}
                    />
                    <h4 className="text-md font-medium mb-2 text-gray-700">
                      Options
                    </h4>
                    {que.options.map((opt, oIndex) => (
                      <div
                        key={opt.option_id || oIndex}
                        className="flex space-x-2 items-center mb-2"
                      >
                        <div className="flex-1">
                          <input
                            type="text"
                            placeholder="Option Label"
                            value={opt.label}
                            onChange={(e) =>
                              handleOptionChange(
                                qIndex,
                                oIndex,
                                "label",
                                e.target.value
                              )
                            }
                            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                              errors[`option_label_${qIndex}_${oIndex}`]
                                ? "border-red-500"
                                : "border-gray-300"
                            }`}
                            disabled={isLoading}
                          />
                          {errors[`option_label_${qIndex}_${oIndex}`] && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors[`option_label_${qIndex}_${oIndex}`]}
                            </p>
                          )}
                        </div>
                        <input
                          type="text"
                          placeholder="Option Value (Optional)"
                          value={opt.value || ""}
                          onChange={(e) =>
                            handleOptionChange(
                              qIndex,
                              oIndex,
                              "value",
                              e.target.value
                            )
                          }
                          className="flex-1 p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 border-gray-300"
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          onClick={() => removeOption(qIndex, oIndex)}
                          className="text-red-600 hover:text-red-800"
                          disabled={isLoading}
                        >
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => addOption(qIndex)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center mt-2"
                      disabled={isLoading}
                    >
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                      Add Option
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addQuestion}
                  className="mb-5 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Add Question
                </button>
                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center ${
                      isLoading ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    {isLoading ? (
                      <div className="spinner mr-2"></div>
                    ) : (
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 3"
                        />
                      </svg>
                    )}
                    Save
                  </button>
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                    disabled={isLoading}
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        );
      };

      const CourseForm = ({ course, onSave, onCancel, assessments }) => {
        const [formData, setFormData] = React.useState(
          course || COURSE_TEMPLATE
        );
        const [errors, setErrors] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        const validateForm = () => {
          const newErrors = {};
          if (!formData.title) newErrors.title = "Course title is required";

          formData.lessons.forEach((lesson, index) => {
            if (!lesson.title)
              newErrors[`lesson_title_${index}`] = "Lesson title is required";
          });

          setErrors(newErrors);
          return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
          e.preventDefault();

          // keep all your existing validation EXCEPT anything about scoring_method
          if (!validateForm()) return;

          setIsLoading(true);
          try {
            // ✅ Only fields your backend accepts
            const basePayload = {
              name: formData.name,
              code: formData.code, // must be one of ['MOCK','REAL','QUIZ']
              version_int: parseInt(formData.version_int),
            };

            if (assessment?.assessment_id) {
              // ===== UPDATE FLOW =====
              await axios.put(
                `${API_BASE_URL}/assessments/${assessment.assessment_id}`,
                basePayload
              );

              // removed items
              for (const dimensionId of removedDimensions) {
                await axios.delete(`${API_BASE_URL}/dimensions/${dimensionId}`);
              }
              for (const questionId of removedQuestions) {
                await axios.delete(`${API_BASE_URL}/questions/${questionId}`);
              }
              for (const optionId of removedOptions) {
                await axios.delete(`${API_BASE_URL}/options/${optionId}`);
              }

              // upsert dimensions
              for (const dim of formData.dimensions) {
                if (dim.dimension_id) {
                  await axios.put(
                    `${API_BASE_URL}/dimensions/${dim.dimension_id}`,
                    {
                      name: dim.name,
                      code: dim.code,
                      sequence: dim.sequence,
                    }
                  );
                } else {
                  const response = await axios.post(
                    `${API_BASE_URL}/assessments/${assessment.assessment_id}/dimensions`,
                    {
                      name: dim.name,
                      code: dim.code,
                      sequence: dim.sequence,
                    }
                  );
                  dim.dimension_id = response.data.dimension_id;
                }
              }

              // upsert questions + options
              for (const q of formData.questions) {
                const dimension = formData.dimensions.find(
                  (d) => d.code === q.dimension_code
                );
                if (q.question_id) {
                  await axios.put(
                    `${API_BASE_URL}/questions/${q.question_id}`,
                    {
                      body_md: q.body_md,
                      dimension_id: dimension?.dimension_id,
                      sequence: q.sequence,
                      block_label: q.block_label || null,
                    }
                  );
                  for (const opt of q.options) {
                    if (opt.option_id) {
                      await axios.put(
                        `${API_BASE_URL}/options/${opt.option_id}`,
                        {
                          label: opt.label,
                          value: opt.value || null,
                        }
                      );
                    } else {
                      const response = await axios.post(
                        `${API_BASE_URL}/questions/${q.question_id}/options`,
                        {
                          label: opt.label,
                          value: opt.value || null,
                        }
                      );
                      opt.option_id = response.data.option_id;
                    }
                  }
                } else {
                  const questionResponse = await axios.post(
                    `${API_BASE_URL}/assessments/${assessment.assessment_id}/questions`,
                    {
                      body_md: q.body_md,
                      dimension_id: dimension?.dimension_id,
                      sequence: q.sequence,
                      block_label: q.block_label || null,
                    }
                  );
                  const newQuestionId = questionResponse.data.question_id;

                  for (const opt of q.options) {
                    const response = await axios.post(
                      `${API_BASE_URL}/questions/${newQuestionId}/options`,
                      {
                        label: opt.label,
                        value: opt.value || null,
                      }
                    );
                    opt.option_id = response.data.option_id;
                  }
                  q.question_id = newQuestionId;
                }
              }
            } else {
              // ===== CREATE FLOW =====
              // 1) create assessment
              const assessRes = await axios.post(
                `${API_BASE_URL}/assessments`,
                basePayload
              );
              const newAssessmentId = assessRes.data.assessment_id;

              // 2) create dimensions
              for (const dim of formData.dimensions) {
                const dRes = await axios.post(
                  `${API_BASE_URL}/assessments/${newAssessmentId}/dimensions`,
                  {
                    name: dim.name,
                    code: dim.code,
                    sequence: dim.sequence,
                  }
                );
                dim.dimension_id = dRes.data.dimension_id;
              }

              // 3) create questions + options
              for (const q of formData.questions) {
                const dimension = formData.dimensions.find(
                  (d) => d.code === q.dimension_code
                );
                const qRes = await axios.post(
                  `${API_BASE_URL}/assessments/${newAssessmentId}/questions`,
                  {
                    body_md: q.body_md,
                    dimension_id: dimension?.dimension_id,
                    sequence: q.sequence,
                    block_label: q.block_label || null,
                  }
                );
                q.question_id = qRes.data.question_id;

                for (const opt of q.options) {
                  const oRes = await axios.post(
                    `${API_BASE_URL}/questions/${q.question_id}/options`,
                    {
                      label: opt.label,
                      value: opt.value || null,
                    }
                  );
                  opt.option_id = oRes.data.option_id;
                }
              }
            }

            setRemovedDimensions([]);
            setRemovedQuestions([]);
            setRemovedOptions([]);
            onSave("Assessment saved successfully", "success");
          } catch (error) {
            console.error("Error saving assessment:", error);
            onSave(
              error.response?.data?.detail || "Failed to save assessment",
              "error"
            );
          } finally {
            setIsLoading(false);
          }
        };

        const handleLessonChange = (index, field, value) => {
          const newLessons = [...formData.lessons];
          newLessons[index][field] = value;
          setFormData({ ...formData, lessons: newLessons });
          setErrors({ ...errors, [`lesson_${field}_${index}`]: null });
        };

        const addLesson = () => {
          setFormData({
            ...formData,
            lessons: [...formData.lessons, { ...LESSON_TEMPLATE }],
          });
        };

        const removeLesson = (index) => {
          const newLessons = formData.lessons.filter((_, i) => i !== index);
          setFormData({ ...formData, lessons: newLessons });
        };

        return (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 modal-overlay">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-2xl font-bold text-gray-800">
                  {course ? "Edit Course" : "Create New Course"}
                </h2>
              </div>
              <form onSubmit={handleSubmit} className="p-6">
                {errors.submit && (
                  <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    {errors.submit}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Title
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) =>
                        setFormData({ ...formData, title: e.target.value })
                      }
                      className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                        errors.title ? "border-red-500" : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors.title && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.title}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Active
                    </label>
                    <select
                      value={formData.is_active}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          is_active: e.target.value === "true",
                        })
                      }
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    >
                      <option value="true">Active</option>
                      <option value="false">Inactive</option>
                    </select>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    disabled={isLoading}
                  />
                </div>

                <h3 className="text-xl font-semibold mb-4 text-gray-800">
                  Lessons
                </h3>
                {formData.lessons.map((lesson, index) => (
                  <div
                    key={index}
                    className="mb-6 p-5 bg-gray-50 rounded-lg border border-gray-200 relative"
                  >
                    <button
                      type="button"
                      onClick={() => removeLesson(index)}
                      className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                      disabled={isLoading}
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <input
                          type="text"
                          placeholder="Lesson Title"
                          value={lesson.title}
                          onChange={(e) =>
                            handleLessonChange(index, "title", e.target.value)
                          }
                          className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                            errors[`lesson_title_${index}`]
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                          disabled={isLoading}
                        />
                        {errors[`lesson_title_${index}`] && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors[`lesson_title_${index}`]}
                          </p>
                        )}
                      </div>

                      <div>
                        <select
                          value={lesson.assessment_id || ""}
                          onChange={(e) =>
                            handleLessonChange(
                              index,
                              "assessment_id",
                              e.target.value || null
                            )
                          }
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          disabled={isLoading}
                        >
                          <option value="">No Assessment</option>
                          {assessments.map((assessment) => (
                            <option
                              key={assessment.assessment_id}
                              value={assessment.assessment_id}
                            >
                              {assessment.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <input
                          type="url"
                          placeholder="Video URL (optional)"
                          value={lesson.video_url}
                          onChange={(e) =>
                            handleLessonChange(
                              index,
                              "video_url",
                              e.target.value
                            )
                          }
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          disabled={isLoading}
                        />
                      </div>

                      <div>
                        <input
                          type="url"
                          placeholder="File URL (optional)"
                          value={lesson.file_url}
                          onChange={(e) =>
                            handleLessonChange(
                              index,
                              "file_url",
                              e.target.value
                            )
                          }
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          disabled={isLoading}
                        />
                      </div>
                    </div>

                    <div>
                      <textarea
                        placeholder="Lesson Content (Markdown supported)"
                        value={lesson.body_md}
                        onChange={(e) =>
                          handleLessonChange(index, "body_md", e.target.value)
                        }
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        rows="4"
                        disabled={isLoading}
                      />
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addLesson}
                  className="mb-6 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  Add Lesson
                </button>

                <div className="flex justify-end space-x-4">
                  <button
                    type="submit"
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                    disabled={isLoading}
                  >
                    {isLoading && <div className="spinner mr-2"></div>}
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {course ? "Update Course" : "Create Course"}
                  </button>
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                    disabled={isLoading}
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        );
      };

      const App = () => {
        const [assessments, setAssessments] = React.useState([]);
        const [sessions, setSessions] = React.useState([]);
        const [courses, setCourses] = React.useState([]);
        const [selectedAssessment, setSelectedAssessment] =
          React.useState(null);
        const [selectedCourse, setSelectedCourse] = React.useState(null);
        const [showForm, setShowForm] = React.useState(false);
        const [showCourseForm, setShowCourseForm] = React.useState(false);
        const [showStudentView, setShowStudentView] = React.useState(null);
        const [showSessionView, setShowSessionView] = React.useState(null);
        const [showSessionResults, setShowSessionResults] =
          React.useState(null);
        const [showSessionResponse, setShowSessionResponse] =
          React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [isLoading, setIsLoading] = React.useState(false);
        const [isAuthenticated, setIsAuthenticated] = React.useState(false);
        const [activeTab, setActiveTab] = React.useState("assessments");

        React.useEffect(() => {
          const token = localStorage.getItem("access_token");
          if (token) {
            setIsAuthenticated(true);
            fetchAssessments();
            fetchSessions();
            fetchCourses();
          }
        }, []);

        const fetchAssessments = async () => {
          setIsLoading(true);
          try {
            const response = await axios.get(`${API_BASE_URL}/assessments`);
            setAssessments(response.data);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to fetch assessments",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const fetchSessions = async () => {
          setIsLoading(true);
          try {
            const response = await axios.get(`${API_BASE_URL}/sessions`);
            setSessions(response.data);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to fetch sessions",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const fetchCourses = async () => {
          setIsLoading(true);
          try {
            const response = await axios.get(`${API_BASE_URL}/courses`);
            setCourses(response.data);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to fetch courses",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleLogin = () => {
          setIsAuthenticated(true);
          fetchAssessments();
          fetchSessions();
        };

        const handleLogout = () => {
          localStorage.removeItem("access_token");
          setIsAuthenticated(false);
          setAssessments([]);
          setSessions([]);
          setSelectedAssessment(null);
          setShowForm(false);
          setShowStudentView(null);
          setShowSessionView(null);
          setShowSessionResults(null);
          setShowSessionResponse(null);
          setNotification({
            message: "Logged out successfully",
            type: "success",
          });
        };

        const handleEdit = async (assessment) => {
          try {
            const [dimensionsResponse, questionsResponse] = await Promise.all([
              axios.get(
                `${API_BASE_URL}/${assessment.assessment_id}/dimensions`
              ),
              axios.get(
                `${API_BASE_URL}/${assessment.assessment_id}/questions`
              ),
            ]);
            setSelectedAssessment({
              ...assessment,
              dimensions: dimensionsResponse.data,
              questions: questionsResponse.data.map((q) => ({
                ...q,
                dimension_code: q.dimension.code,
                options: q.options,
              })),
            });
            setShowForm(true);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail ||
                "Failed to load assessment details",
              type: "error",
            });
          }
        };

        const handleDelete = async (assessmentId) => {
          if (
            window.confirm("Are you sure you want to delete this assessment?")
          ) {
            setIsLoading(true);
            try {
              await axios.delete(`${API_BASE_URL}/assessments/${assessmentId}`);
              setNotification({
                message: "Assessment deleted successfully",
                type: "success",
              });
              await fetchAssessments();
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to delete assessment",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          }
        };

        const handleStartSession = async (assessmentId, assessmentName) => {
          setIsLoading(true);
          try {
            const response = await axios.post(`${API_BASE_URL}/sessions`, {
              assessment_id: assessmentId,
            });
            setNotification({
              message: `Session created for ${assessmentName}`,
              type: "success",
            });
            await fetchSessions();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to create session",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleViewAsStudent = (assessmentId, assessmentName) => {
          setShowStudentView({ assessmentId, assessmentName });
        };

        const handleSessionView = (sessionId) => {
          setShowSessionView(sessionId);
        };

        const handleSessionResults = (sessionId) => {
          setShowSessionResults(sessionId);
        };

        const handleSubmitResponses = (sessionId, assessmentId) => {
          setShowSessionResponse({ sessionId, assessmentId });
        };

        const handleDeleteSession = async (sessionId) => {
          if (window.confirm("Are you sure you want to delete this session?")) {
            setIsLoading(true);
            try {
              await axios.delete(`${API_BASE_URL}/sessions/${sessionId}`);
              setNotification({
                message: "Session deleted successfully",
                type: "success",
              });
              await fetchSessions();
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to delete session",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          }
        };

        const handleSave = (message, type) => {
          setShowForm(false);
          setSelectedAssessment(null);
          setNotification({ message, type });
          fetchAssessments();
        };

        const handleCourseSave = (savedCourse) => {
          setShowCourseForm(false);
          setSelectedCourse(null);
          setNotification({
            message: "Course saved successfully!",
            type: "success",
          });
          fetchCourses();
        };

        const handleEditCourse = (course) => {
          setSelectedCourse(course);
          setShowCourseForm(true);
        };

        const handleDeleteCourse = async (courseId) => {
          if (!confirm("Are you sure you want to delete this course?")) return;

          setIsLoading(true);
          try {
            await axios.delete(`${API_BASE_URL}/courses/${courseId}`);
            setNotification({
              message: "Course deleted successfully!",
              type: "success",
            });
            fetchCourses();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to delete course",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleCancel = () => {
          setShowForm(false);
          setSelectedAssessment(null);
        };

        const closeNotification = () => {
          setNotification(null);
        };

        if (!isAuthenticated) {
          return <LoginModal onLogin={handleLogin} />;
        }

        return (
          <div className="container mx-auto p-6 max-w-7xl">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-4xl font-bold text-gray-800 flex items-center">
                <svg
                  className="w-8 h-8 mr-3 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                Assessment Manager
              </h1>
              <button
                onClick={handleLogout}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                Logout
              </button>
            </div>
            <div className="flex mb-6 border-b border-gray-200">
              <button
                onClick={() => setActiveTab("assessments")}
                className={`px-4 py-2 text-gray-600 hover:text-gray-800 ${
                  activeTab === "assessments" ? "tab-active" : ""
                }`}
              >
                Assessments
              </button>
              <button
                onClick={() => setActiveTab("sessions")}
                className={`px-4 py-2 text-gray-600 hover:text-gray-800 ${
                  activeTab === "sessions" ? "tab-active" : ""
                }`}
              >
                Sessions
              </button>
              <button
                onClick={() => setActiveTab("courses")}
                className={`px-4 py-2 text-gray-600 hover:text-gray-800 ${
                  activeTab === "courses" ? "tab-active" : ""
                }`}
              >
                Courses
              </button>
            </div>
            {activeTab === "assessments" && (
              <>
                <button
                  onClick={() => setShowForm(true)}
                  className="mb-6 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors hover-scale flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create New Assessment
                </button>
                {isLoading && (
                  <div className="flex justify-center mb-6">
                    <div className="spinner"></div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {assessments.map((assessment) => (
                    <AssessmentCard
                      key={assessment.assessment_id}
                      assessment={assessment}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      onViewAsStudent={handleViewAsStudent}
                      onStartSession={handleStartSession}
                    />
                  ))}
                </div>
              </>
            )}
            {activeTab === "sessions" && (
              <>
                {isLoading && (
                  <div className="flex justify-center mb-6">
                    <div className="spinner"></div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {sessions.map((session) => (
                    <SessionCard
                      key={session.session_id}
                      session={session}
                      onView={handleSessionView}
                      onDelete={handleDeleteSession}
                      onSubmitResponses={handleSubmitResponses}
                      onViewResults={handleSessionResults}
                    />
                  ))}
                </div>
              </>
            )}
            {activeTab === "courses" && (
              <>
                <button
                  onClick={() => setShowCourseForm(true)}
                  className="mb-6 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors hover-scale flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  Create New Course
                </button>
                {isLoading && (
                  <div className="flex justify-center mb-6">
                    <div className="spinner"></div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {courses.map((course) => (
                    <CourseCard
                      key={course.course_id}
                      course={course}
                      onEdit={handleEditCourse}
                      onDelete={handleDeleteCourse}
                    />
                  ))}
                </div>
              </>
            )}
            {notification && (
              <Notification
                message={notification.message}
                type={notification.type}
                onClose={closeNotification}
                details={notification.details}
              />
            )}
            {showForm && (
              <AssessmentForm
                assessment={selectedAssessment}
                onSave={handleSave}
                onCancel={handleCancel}
                setNotification={setNotification}
              />
            )}
            {showCourseForm && (
              <CourseForm
                course={selectedCourse}
                onSave={handleCourseSave}
                onCancel={() => {
                  setShowCourseForm(false);
                  setSelectedCourse(null);
                }}
                assessments={assessments}
              />
            )}
            {showStudentView && (
              <StudentViewModal
                assessmentId={showStudentView.assessmentId}
                assessmentName={showStudentView.assessmentName}
                onClose={() => setShowStudentView(null)}
                setNotification={setNotification}
              />
            )}
            {showSessionView && (
              <SessionViewModal
                sessionId={showSessionView}
                onClose={() => setShowSessionView(null)}
                setNotification={setNotification}
              />
            )}
            {showSessionResults && (
              <SessionResultsModal
                sessionId={showSessionResults}
                onClose={() => setShowSessionResults(null)}
                setNotification={setNotification}
              />
            )}
            {showSessionResponse && (
              <SessionResponseModal
                sessionId={showSessionResponse.sessionId}
                assessmentId={showSessionResponse.assessmentId}
                onClose={() => setShowSessionResponse(null)}
                setNotification={setNotification}
              />
            )}
          </div>
        );
      };

      const root = ReactDOM.createRoot(document.getElementById("root"));
      root.render(<App />);
    </script>
  </body>
</html>
