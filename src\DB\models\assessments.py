# SQLAlchemy models
import uuid
from datetime import datetime, timed<PERSON><PERSON>

from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Numeric, Integer, JSON, Enum, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base, relationship
from src.DB.database import Base
from src.DB.enums import AssessmentType, CourseContentType

#################
###Assessments###
#################

class Assessment(Base):
    """Defines a  assessment including its metadata,
     and associated content like questions, dimensions, and result profiles.
    """
    __tablename__ = "assessments"
    assessment_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)  # Unique ID for the assessment
    code = Column(Enum(AssessmentType), nullable=False)  # Short code identifier or abbreviation
    name = Column(String, nullable=False)  # Human-readable name
    version_int = Column(Integer, default=1, nullable=False)  # Version number
    active = Column(<PERSON><PERSON><PERSON>, default=True, nullable=False)  # Is the assessment currently active?

    """Stores descriptions for an assessment."""
    directions = Column(Text, nullable=True)  # Instructions or introduction text
    description = Column(Text, nullable=True)  # Overview or summary
    notes = Column(JSON, nullable=True)  # Additional notes or context
    reference = Column(JSON, nullable=True)  # Overview or summary
    conclusion = Column(Text, nullable=True)  # Closing remarks or feedback
    timing = Column(Integer, nullable=True)  # Estimated time to complete in minutes

    dimensions = relationship("Dimension", back_populates="assessment", cascade="all, delete-orphan", lazy="selectin")
    questions = relationship("Question", back_populates="assessment", cascade="all, delete-orphan", lazy="selectin")
    result_profiles = relationship("ResultProfile", back_populates="assessment", cascade="all, delete-orphan", lazy="selectin")
    sessions = relationship("UserSession", back_populates="assessment", cascade="all, delete-orphan", lazy="selectin")

class Dimension(Base):
    """Represents a measurable trait or axis in the assessment (e.g., Introversion, Openness)."""
    __tablename__ = "dimensions"
    dimension_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    assessment_id = Column(UUID(as_uuid=True), ForeignKey("assessments.assessment_id", ondelete="CASCADE"), nullable=False)
    code = Column(String, nullable=False)  # Short code or abbreviation
    name = Column(String, nullable=False)  # Full name
    sequence = Column(Integer, default=0)  # Order in UI or scoring logic

    assessment = relationship("Assessment", back_populates="dimensions")
    questions = relationship("Question", back_populates="dimension", cascade="all, delete-orphan")

    option_scores = relationship("OptionScore", back_populates="dimension")
    # scores = relationship("DimensionScore", back_populates="dimension")

class Question(Base):
    """A single question within an assessment. Each is tied to a dimension.
    """
    __tablename__ = "questions"
    question_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    assessment_id = Column(UUID(as_uuid=True), ForeignKey("assessments.assessment_id", ondelete="CASCADE"), nullable=False)
    dimension_id = Column(UUID(as_uuid=True), ForeignKey("dimensions.dimension_id", ondelete="CASCADE"), nullable=False)
    body_md = Column(Text, nullable=False)  # Markdown-supported question body
    sequence = Column(Integer, default=0)  # Order in which the question appears
    block_label = Column(String, nullable=True)  # Optional block grouping

    assessment = relationship("Assessment", back_populates="questions", lazy="selectin")
    dimension = relationship("Dimension", back_populates="questions", lazy="selectin")
    options = relationship("Option", back_populates="question", cascade="all, delete-orphan", lazy="selectin")

class Option(Base):
    """An answer option for a specific question. May affect dimension scores.
    """
    __tablename__ = "options"
    option_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey("questions.question_id", ondelete="CASCADE"), nullable=False)
    label = Column(String, nullable=False)  # The visible answer choice
    value = Column(String, nullable=True)  # Optional value used in logic
    is_correct = Column(Boolean, default=False, nullable=True) #used in checking the exams
    comment = Column(Text, nullable=True)  # Optional value used in logic

    question = relationship("Question", back_populates="options")
    scores = relationship("OptionScore", back_populates="option", lazy="selectin")

#############
###Courses###
#############

class Course(Base):
    """
    Represents a structured learning course containing an ordered timeline of 
    lessons and/or assessments (mock or real). A course may have only assessments.
    """
    __tablename__ = "courses"
    course_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)  # Unique course ID
    title = Column(String, nullable=False)  # Course display name
    description = Column(Text, nullable=True)  # Optional description for UI
    is_active = Column(Boolean, default=True, nullable=False)  # Can this course be enrolled/viewed?
    created_at = Column(DateTime(timezone=True), default=datetime.now)  # Course creation time

    contents = relationship("CourseContent", back_populates="course", cascade="all, delete-orphan", lazy="selectin")

class CourseContent(Base):
    """
    Defines the timeline for a course. Each entry links either a Lesson or an Assessment
    and defines the order (sequence) in which the item appears in the course.
    Only one of `lesson_id` or `assessment_id` must be set per row.
    """
    __tablename__ = "course_contents"
    
    content_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)  # Unique row ID

    course_id = Column(UUID(as_uuid=True), ForeignKey("courses.course_id", ondelete="CASCADE"), nullable=False)  # Linked course
    content_type = Column(Enum(CourseContentType), nullable=False)  # Type of content

    lesson_id = Column(UUID(as_uuid=True), ForeignKey("lessons.lesson_id", ondelete="CASCADE"), nullable=True)  # FK to lesson
    assessment_id = Column(UUID(as_uuid=True), ForeignKey("assessments.assessment_id", ondelete="CASCADE"), nullable=True)  # FK to assessment

    sequence = Column(Integer, default=0, nullable=False)  # Position in course timeline

    course = relationship("Course", back_populates="contents")
    lesson = relationship("Lesson", lazy="selectin")
    assessment = relationship("Assessment", lazy="selectin")

class Lesson(Base):
    """
    A single educational unit in a course. It can contain textual content, video content, or both.
    """
    __tablename__ = "lessons"
    lesson_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)  # Unique lesson ID
    title = Column(String, nullable=False)  # Lesson name or headline
    body_md = Column(Text, nullable=True)  # Markdown-compatible text content
    video_url = Column(String, nullable=True)  # Optional video URL
    file_url = Column(String, nullable=True)  # Optional file URL
    created_at = Column(DateTime(timezone=True), default=datetime.now)  # Timestamp



#############
###Results###
#############


class ResultProfile(Base):
    """Represents predefined personality types or outcome profiles (e.g., "INTJ", "Dominant")
    used to categorize users after completing an assessment.
    """
    __tablename__ = "result_profiles"
    profile_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    assessment_id = Column(UUID(as_uuid=True), ForeignKey("assessments.assessment_id", ondelete="CASCADE"), nullable=False)
    code = Column(String, nullable=False)  # Short profile code
    name = Column(String, nullable=False)  # Full name or label
    description = Column(Text, nullable=True)  # Description shown to the user
    criteria_json = Column(JSON, nullable=True)  # Optional logic used to assign the profile

    assessment = relationship("Assessment", back_populates="result_profiles")
    sessions = relationship("SessionProfile", back_populates="profile")

class UserSession(Base):
    """Represents a user's attempt or instance of completing an assessment.
    Includes timestamps and links to responses, scores, and result.
    """
    __tablename__ = "user_sessions"
    session_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.user_id"), nullable=False)
    assessment_id = Column(UUID(as_uuid=True), ForeignKey("assessments.assessment_id"), nullable=False)
    started_at = Column(DateTime(timezone=True), default=datetime.now)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    user = relationship("User", back_populates="assessment_sessions", lazy="selectin")
    assessment = relationship("Assessment", back_populates="sessions", lazy="selectin")
    responses = relationship("Response", back_populates="session", cascade="all, delete-orphan", lazy="selectin")
    # dimension_scores = relationship("DimensionScore", back_populates="session", cascade="all, delete-orphan")
    session_profile = relationship("SessionProfile", uselist=False, back_populates="session", cascade="all, delete-orphan", lazy="selectin")

class Response(Base):
    """Captures an individual user's answer to a question within a session.
    Links to the selected option and timestamp.
    """
    __tablename__ = "responses"
    response_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.session_id", ondelete="CASCADE"), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey("questions.question_id"), nullable=False)
    option_id = Column(UUID(as_uuid=True), ForeignKey("options.option_id"), nullable=True)
    value = Column(String, nullable=True)  # Used in open-ended or scaled responses
    answered_at = Column(DateTime(timezone=True), default=datetime.now)

    session = relationship("UserSession", back_populates="responses")
class SessionProfile(Base):
    """Maps a session to a result profile and includes optional confidence score.
    """
    __tablename__ = "session_profile"
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.session_id", ondelete="CASCADE"), primary_key=True)
    profile_id = Column(UUID(as_uuid=True), ForeignKey("result_profiles.profile_id"), nullable=False)
    confidence_pct = Column(Numeric, nullable=True)  # Optional confidence level

    session = relationship("UserSession", back_populates="session_profile")
    profile = relationship("ResultProfile", back_populates="sessions")

class OptionScore(Base):
    """Maps how selecting a given option affects a specific dimension (e.g., +1 to Extraversion).
    """
    __tablename__ = "option_scores"
    option_id = Column(UUID(as_uuid=True), ForeignKey("options.option_id", ondelete="CASCADE"), primary_key=True)
    dimension_id = Column(UUID(as_uuid=True), ForeignKey("dimensions.dimension_id", ondelete="CASCADE"), primary_key=True)
    delta = Column(Numeric, nullable=False)  # Amount added to the dimension score
    is_correct = Column(Boolean, default=False, nullable=True) #used in checking the exams

    option = relationship("Option", back_populates="scores", lazy="selectin")
    dimension = relationship("Dimension", back_populates="option_scores", lazy="selectin")

class UserLessonProgress(Base):
    __tablename__ = "user_lesson_progress"
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.user_id"), primary_key=True)
    lesson_id = Column(UUID(as_uuid=True), ForeignKey("lessons.lesson_id"), primary_key=True)
    viewed_at = Column(DateTime(timezone=True), default=datetime.now)
    completed = Column(Boolean, default=False)

    lesson = relationship("Lesson", lazy="selectin")

# class DimensionScore(Base):
#     """Represents the user's computed score for a given dimension within a session.
#     Includes raw, standardized, and percentile values.
#     """
#     __tablename__ = "dimension_scores"
#     session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.session_id", ondelete="CASCADE"), primary_key=True)
#     dimension_id = Column(UUID(as_uuid=True), ForeignKey("dimensions.dimension_id"), primary_key=True)
#     raw_score = Column(Numeric, nullable=False)  # Sum of deltas
#     standard_score = Column(Numeric, nullable=True)  # Optional z-score
#     percentile = Column(Numeric, nullable=True)  # Optional percentile

#     session = relationship("UserSession", back_populates="dimension_scores")
#     dimension = relationship("Dimension", back_populates="scores")

